<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o 2.AT8236_CSS.out -m2.AT8236_CSS.map -iD:/TI/mspm0_sdk_2_05_00_05/source -iD:/TI/FourLine -iD:/TI/FourLine/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=2.AT8236_CSS_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./BSP/Four_linewalking.o ./BSP/bsp_at8236.o ./BSP/delay.o ./BSP/usart.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688a4870</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\TI\FourLine\Debug\2.AT8236_CSS.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xc1d</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\TI\FourLine\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\TI\FourLine\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\TI\FourLine\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\TI\FourLine\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>Four_linewalking.o</file>
         <name>Four_linewalking.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\TI\FourLine\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_at8236.o</file>
         <name>bsp_at8236.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\TI\FourLine\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\TI\FourLine\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>usart.o</file>
         <name>usart.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>D:\TI\FourLine\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.Four_LineWalking</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x110</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x1d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text</name>
         <load_address>0x2d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d4</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text:memcpy</name>
         <load_address>0x3ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3ac</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x446</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x446</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.SYSCFG_DL_PWM_L1_init</name>
         <load_address>0x448</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x448</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.SYSCFG_DL_PWM_L2_init</name>
         <load_address>0x4d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4d8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.SYSCFG_DL_PWM_R1_init</name>
         <load_address>0x568</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x568</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.SYSCFG_DL_PWM_R2_init</name>
         <load_address>0x5f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.Motion_Car_Control</name>
         <load_address>0x688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x688</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.__mulsf3</name>
         <load_address>0x714</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x714</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x81c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x81c</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x88c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x88c</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x8fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8fc</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x95c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x95c</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_UART_init</name>
         <load_address>0x9bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9bc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xa04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa04</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.UART1_IRQHandler</name>
         <load_address>0xa48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa48</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.delay_ms</name>
         <load_address>0xa8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa8c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.__floatsisf</name>
         <load_address>0xacc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xacc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xb08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb08</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.__muldsi3</name>
         <load_address>0xb44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb44</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.__fixsfsi</name>
         <load_address>0xb80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb80</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.init_motor</name>
         <load_address>0xbb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbb8</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xbec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbec</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xc1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc1c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xc44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc44</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xc60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc60</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0xc7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc7c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0xc98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0xcb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcb0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xcc6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0xcdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcdc</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xcee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcee</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0xd00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd00</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xd10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd10</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xd1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd1c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-98">
         <name>.text:abort</name>
         <load_address>0xd24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd24</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.HOSTexit</name>
         <load_address>0xd2a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd2a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xd2e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd2e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0xd32</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd32</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-177">
         <name>.cinit..data.load</name>
         <load_address>0xd80</load_address>
         <readonly>true</readonly>
         <run_address>0xd80</run_address>
         <size>0xc</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-175">
         <name>__TI_handler_table</name>
         <load_address>0xd8c</load_address>
         <readonly>true</readonly>
         <run_address>0xd8c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-178">
         <name>.cinit..bss.load</name>
         <load_address>0xd98</load_address>
         <readonly>true</readonly>
         <run_address>0xd98</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-176">
         <name>__TI_cinit_table</name>
         <load_address>0xda0</load_address>
         <readonly>true</readonly>
         <run_address>0xda0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-116">
         <name>.rodata.gUART_0Config</name>
         <load_address>0xd38</load_address>
         <readonly>true</readonly>
         <run_address>0xd38</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-118">
         <name>.rodata.gUART_1Config</name>
         <load_address>0xd42</load_address>
         <readonly>true</readonly>
         <run_address>0xd42</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.rodata.gPWM_L1Config</name>
         <load_address>0xd4c</load_address>
         <readonly>true</readonly>
         <run_address>0xd4c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.rodata.gPWM_L2Config</name>
         <load_address>0xd54</load_address>
         <readonly>true</readonly>
         <run_address>0xd54</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-109">
         <name>.rodata.gPWM_R1Config</name>
         <load_address>0xd5c</load_address>
         <readonly>true</readonly>
         <run_address>0xd5c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.rodata.gPWM_R2Config</name>
         <load_address>0xd64</load_address>
         <readonly>true</readonly>
         <run_address>0xd64</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.rodata.gPWM_L1ClockConfig</name>
         <load_address>0xd6c</load_address>
         <readonly>true</readonly>
         <run_address>0xd6c</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.rodata.gPWM_L2ClockConfig</name>
         <load_address>0xd6f</load_address>
         <readonly>true</readonly>
         <run_address>0xd6f</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-108">
         <name>.rodata.gPWM_R1ClockConfig</name>
         <load_address>0xd72</load_address>
         <readonly>true</readonly>
         <run_address>0xd72</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.rodata.gPWM_R2ClockConfig</name>
         <load_address>0xd75</load_address>
         <readonly>true</readonly>
         <run_address>0xd75</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-115">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0xd78</load_address>
         <readonly>true</readonly>
         <run_address>0xd78</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-117">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0xd7a</load_address>
         <readonly>true</readonly>
         <run_address>0xd7a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e3">
         <name>.data.pid_output_IRR</name>
         <load_address>0x202001e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.data.err</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.data.IRR_SPEED</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.data.recv0_buff</name>
         <load_address>0x20200160</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200160</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.recv0_length</name>
         <load_address>0x202001ec</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ec</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.data.recv0_flag</name>
         <load_address>0x202001ee</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ee</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.bss.APP_IR_PID_Calc.IRTrack_Integral</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020015c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.common:gPWM_R1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-c4">
         <name>.common:gPWM_L2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_loc</name>
         <load_address>0x24d</load_address>
         <run_address>0x24d</run_address>
         <size>0x184</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_loc</name>
         <load_address>0x3d1</load_address>
         <run_address>0x3d1</run_address>
         <size>0x246</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_loc</name>
         <load_address>0x617</load_address>
         <run_address>0x617</run_address>
         <size>0x1da</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_loc</name>
         <load_address>0x7f1</load_address>
         <run_address>0x7f1</run_address>
         <size>0x43</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_loc</name>
         <load_address>0x834</load_address>
         <run_address>0x834</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_loc</name>
         <load_address>0x847</load_address>
         <run_address>0x847</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_loc</name>
         <load_address>0x226e</load_address>
         <run_address>0x226e</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x2a2a</load_address>
         <run_address>0x2a2a</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_loc</name>
         <load_address>0x2b02</load_address>
         <run_address>0x2b02</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x2f26</load_address>
         <run_address>0x2f26</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x3092</load_address>
         <run_address>0x3092</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x3101</load_address>
         <run_address>0x3101</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_loc</name>
         <load_address>0x3268</load_address>
         <run_address>0x3268</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x23b</load_address>
         <run_address>0x23b</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_abbrev</name>
         <load_address>0x2a8</load_address>
         <run_address>0x2a8</run_address>
         <size>0x47</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_abbrev</name>
         <load_address>0x2ef</load_address>
         <run_address>0x2ef</run_address>
         <size>0x19c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0x48b</load_address>
         <run_address>0x48b</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_abbrev</name>
         <load_address>0x5f2</load_address>
         <run_address>0x5f2</run_address>
         <size>0x105</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_abbrev</name>
         <load_address>0x6f7</load_address>
         <run_address>0x6f7</run_address>
         <size>0x1df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_abbrev</name>
         <load_address>0x8d6</load_address>
         <run_address>0x8d6</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_abbrev</name>
         <load_address>0x938</load_address>
         <run_address>0x938</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_abbrev</name>
         <load_address>0xbbe</load_address>
         <run_address>0xbbe</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0xe59</load_address>
         <run_address>0xe59</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0xf08</load_address>
         <run_address>0xf08</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_abbrev</name>
         <load_address>0x1078</load_address>
         <run_address>0x1078</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0x10b1</load_address>
         <run_address>0x10b1</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x1173</load_address>
         <run_address>0x1173</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x11e3</load_address>
         <run_address>0x11e3</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_abbrev</name>
         <load_address>0x1270</load_address>
         <run_address>0x1270</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_abbrev</name>
         <load_address>0x1308</load_address>
         <run_address>0x1308</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_abbrev</name>
         <load_address>0x1334</load_address>
         <run_address>0x1334</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_abbrev</name>
         <load_address>0x135b</load_address>
         <run_address>0x135b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_abbrev</name>
         <load_address>0x1382</load_address>
         <run_address>0x1382</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_abbrev</name>
         <load_address>0x13a9</load_address>
         <run_address>0x13a9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x13d0</load_address>
         <run_address>0x13d0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_abbrev</name>
         <load_address>0x13f7</load_address>
         <run_address>0x13f7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_abbrev</name>
         <load_address>0x141e</load_address>
         <run_address>0x141e</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_abbrev</name>
         <load_address>0x1443</load_address>
         <run_address>0x1443</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x335f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x335f</load_address>
         <run_address>0x335f</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_info</name>
         <load_address>0x33df</load_address>
         <run_address>0x33df</run_address>
         <size>0x79</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_info</name>
         <load_address>0x3458</load_address>
         <run_address>0x3458</run_address>
         <size>0xa09</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x3e61</load_address>
         <run_address>0x3e61</run_address>
         <size>0xa45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_info</name>
         <load_address>0x48a6</load_address>
         <run_address>0x48a6</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x4a3b</load_address>
         <run_address>0x4a3b</run_address>
         <size>0xabd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_info</name>
         <load_address>0x54f8</load_address>
         <run_address>0x54f8</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_info</name>
         <load_address>0x556d</load_address>
         <run_address>0x556d</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_info</name>
         <load_address>0x86df</load_address>
         <run_address>0x86df</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x9985</load_address>
         <run_address>0x9985</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0x9da8</load_address>
         <run_address>0x9da8</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0xa4ec</load_address>
         <run_address>0xa4ec</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0xa532</load_address>
         <run_address>0xa532</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xa6c4</load_address>
         <run_address>0xa6c4</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xa78a</load_address>
         <run_address>0xa78a</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0xa906</load_address>
         <run_address>0xa906</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_info</name>
         <load_address>0xa9fe</load_address>
         <run_address>0xa9fe</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_info</name>
         <load_address>0xaa39</load_address>
         <run_address>0xaa39</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_info</name>
         <load_address>0xabe0</load_address>
         <run_address>0xabe0</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_info</name>
         <load_address>0xad6d</load_address>
         <run_address>0xad6d</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0xaefc</load_address>
         <run_address>0xaefc</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0xb08f</load_address>
         <run_address>0xb08f</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_info</name>
         <load_address>0xb228</load_address>
         <run_address>0xb228</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_info</name>
         <load_address>0xb3b7</load_address>
         <run_address>0xb3b7</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_info</name>
         <load_address>0xb6b1</load_address>
         <run_address>0xb6b1</run_address>
         <size>0x88</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_ranges</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_ranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0xf8</load_address>
         <run_address>0xf8</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_ranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_ranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_ranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x590</load_address>
         <run_address>0x590</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_ranges</name>
         <load_address>0x5e0</load_address>
         <run_address>0x5e0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x21fb</load_address>
         <run_address>0x21fb</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_str</name>
         <load_address>0x233a</load_address>
         <run_address>0x233a</run_address>
         <size>0xcb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_str</name>
         <load_address>0x2405</load_address>
         <run_address>0x2405</run_address>
         <size>0x532</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x2937</load_address>
         <run_address>0x2937</run_address>
         <size>0x506</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_str</name>
         <load_address>0x2e3d</load_address>
         <run_address>0x2e3d</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_str</name>
         <load_address>0x2f64</load_address>
         <run_address>0x2f64</run_address>
         <size>0x897</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_str</name>
         <load_address>0x37fb</load_address>
         <run_address>0x37fb</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_str</name>
         <load_address>0x3972</load_address>
         <run_address>0x3972</run_address>
         <size>0x1dd6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_str</name>
         <load_address>0x5748</load_address>
         <run_address>0x5748</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x6435</load_address>
         <run_address>0x6435</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x665a</load_address>
         <run_address>0x665a</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_str</name>
         <load_address>0x6989</load_address>
         <run_address>0x6989</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x6a7e</load_address>
         <run_address>0x6a7e</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x6c19</load_address>
         <run_address>0x6c19</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x6d81</load_address>
         <run_address>0x6d81</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_str</name>
         <load_address>0x6f56</load_address>
         <run_address>0x6f56</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_str</name>
         <load_address>0x709e</load_address>
         <run_address>0x709e</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x168</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x198</load_address>
         <run_address>0x198</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_frame</name>
         <load_address>0x1b8</load_address>
         <run_address>0x1b8</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_frame</name>
         <load_address>0x21c</load_address>
         <run_address>0x21c</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_frame</name>
         <load_address>0x2cc</load_address>
         <run_address>0x2cc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x314</load_address>
         <run_address>0x314</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_frame</name>
         <load_address>0x37c</load_address>
         <run_address>0x37c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_frame</name>
         <load_address>0x39c</load_address>
         <run_address>0x39c</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_frame</name>
         <load_address>0x7a4</load_address>
         <run_address>0x7a4</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x95c</load_address>
         <run_address>0x95c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_frame</name>
         <load_address>0x9ec</load_address>
         <run_address>0x9ec</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0xaec</load_address>
         <run_address>0xaec</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0xb0c</load_address>
         <run_address>0xb0c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0xb44</load_address>
         <run_address>0xb44</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0xb6c</load_address>
         <run_address>0xb6c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_frame</name>
         <load_address>0xb9c</load_address>
         <run_address>0xb9c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_frame</name>
         <load_address>0xbcc</load_address>
         <run_address>0xbcc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x864</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x864</load_address>
         <run_address>0x864</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_line</name>
         <load_address>0x91c</load_address>
         <run_address>0x91c</run_address>
         <size>0x45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x961</load_address>
         <run_address>0x961</run_address>
         <size>0x327</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0xc88</load_address>
         <run_address>0xc88</run_address>
         <size>0x31a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_line</name>
         <load_address>0xfa2</load_address>
         <run_address>0xfa2</run_address>
         <size>0x1e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_line</name>
         <load_address>0x1183</load_address>
         <run_address>0x1183</run_address>
         <size>0x398</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_line</name>
         <load_address>0x151b</load_address>
         <run_address>0x151b</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_line</name>
         <load_address>0x1694</load_address>
         <run_address>0x1694</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_line</name>
         <load_address>0x2e03</load_address>
         <run_address>0x2e03</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x381b</load_address>
         <run_address>0x381b</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_line</name>
         <load_address>0x39f7</load_address>
         <run_address>0x39f7</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_line</name>
         <load_address>0x3f11</load_address>
         <run_address>0x3f11</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x3f4f</load_address>
         <run_address>0x3f4f</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x404d</load_address>
         <run_address>0x404d</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x410d</load_address>
         <run_address>0x410d</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_line</name>
         <load_address>0x42d5</load_address>
         <run_address>0x42d5</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_line</name>
         <load_address>0x433c</load_address>
         <run_address>0x433c</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_line</name>
         <load_address>0x437d</load_address>
         <run_address>0x437d</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_line</name>
         <load_address>0x4484</load_address>
         <run_address>0x4484</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0x4564</load_address>
         <run_address>0x4564</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x461c</load_address>
         <run_address>0x461c</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x46d8</load_address>
         <run_address>0x46d8</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_line</name>
         <load_address>0x477c</load_address>
         <run_address>0x477c</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_line</name>
         <load_address>0x4835</load_address>
         <run_address>0x4835</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xc78</size>
         <contents>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xd80</load_address>
         <run_address>0xd80</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-176"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xd38</load_address>
         <run_address>0xd38</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-117"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-13f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200160</run_address>
         <size>0x8f</size>
         <contents>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x160</size>
         <contents>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-17a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-136" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-137" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-138" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-139" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13b" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13d" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-159" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x328e</size>
         <contents>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-9e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1452</size>
         <contents>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-17c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb739</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-17b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x620</size>
         <contents>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-7f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-161" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7187</size>
         <contents>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-124"/>
         </contents>
      </logical_group>
      <logical_group id="lg-163" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbec</size>
         <contents>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-165" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48d5</size>
         <contents>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-80"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16f" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe8</size>
         <contents>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-7d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-179" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-181" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdb0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-182" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1ef</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-183" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xdb0</used_space>
         <unused_space>0x1f250</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xc78</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd38</start_address>
               <size>0x48</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd80</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xdb0</start_address>
               <size>0x1f250</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x3ef</used_space>
         <unused_space>0x7c11</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13b"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x160</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200160</start_address>
               <size>0x8f</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001ef</start_address>
               <size>0x7c11</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0xd80</load_address>
            <load_size>0xc</load_size>
            <run_address>0x20200160</run_address>
            <run_size>0x8f</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0xd98</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x160</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xda0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xdb0</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xdb0</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xd8c</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xd98</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-73">
         <name>SYSCFG_DL_init</name>
         <value>0xa05</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-74">
         <name>SYSCFG_DL_initPower</name>
         <value>0x88d</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-75">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x81d</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-76">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xbed</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-77">
         <name>SYSCFG_DL_PWM_L1_init</name>
         <value>0x449</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-78">
         <name>SYSCFG_DL_PWM_R1_init</name>
         <value>0x569</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-79">
         <name>SYSCFG_DL_PWM_L2_init</name>
         <value>0x4d9</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SYSCFG_DL_PWM_R2_init</name>
         <value>0x5f9</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-7b">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x8fd</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-7c">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x95d</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-7d">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0xc7d</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-7e">
         <name>gPWM_R1Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-7f">
         <name>gPWM_L2Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-8a">
         <name>Default_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>Reset_Handler</name>
         <value>0xd2f</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-8c">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-8d">
         <name>NMI_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>HardFault_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>SVC_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>PendSV_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>SysTick_Handler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>GROUP0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>GROUP1_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>TIMG8_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>UART3_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>ADC0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>ADC1_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>CANFD0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>DAC0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>SPI0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>SPI1_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>UART2_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>UART0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>TIMG0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>TIMG6_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a0">
         <name>TIMA0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a1">
         <name>TIMA1_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a2">
         <name>TIMG7_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a3">
         <name>TIMG12_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a4">
         <name>I2C0_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a5">
         <name>I2C1_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a6">
         <name>AES_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a7">
         <name>RTC_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a8">
         <name>DMA_IRQHandler</name>
         <value>0x447</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-b0">
         <name>main</name>
         <value>0xcb1</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-bd">
         <name>Four_LineWalking</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-be">
         <name>err</name>
         <value>0x202001e4</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-bf">
         <name>pid_output_IRR</name>
         <value>0x202001e8</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-c0">
         <name>IRR_SPEED</name>
         <value>0x202001e0</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-ce">
         <name>init_motor</name>
         <value>0xbb9</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-cf">
         <name>Motion_Car_Control</name>
         <value>0x689</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-da">
         <name>delay_ms</name>
         <value>0xa8d</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-e5">
         <name>UART1_IRQHandler</name>
         <value>0xa49</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-e6">
         <name>recv0_length</name>
         <value>0x202001ec</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-e7">
         <name>recv0_buff</name>
         <value>0x20200160</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-e8">
         <name>recv0_flag</name>
         <value>0x202001ee</value>
         <object_component_ref idref="oc-5e"/>
      </symbol>
      <symbol id="sm-e9">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ea">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-eb">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ec">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ed">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f0">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f1">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fa">
         <name>DL_Common_delayCycles</name>
         <value>0xd11</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-111">
         <name>DL_Timer_setClockConfig</name>
         <value>0xc61</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-112">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0xd01</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-113">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xc45</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-114">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0xc99</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-115">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x1d1</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-122">
         <name>DL_UART_init</name>
         <value>0x9bd</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-123">
         <name>DL_UART_setClockConfig</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-12e">
         <name>_c_int00_noargs</name>
         <value>0xc1d</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-12f">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-13b">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xb09</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-143">
         <name>_system_pre_init</name>
         <value>0xd33</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-14e">
         <name>__TI_zero_init_nomemset</name>
         <value>0xcc7</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-157">
         <name>__TI_decompress_none</name>
         <value>0xcef</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-162">
         <name>__TI_decompress_lzss</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-16c">
         <name>abort</name>
         <value>0xd25</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-176">
         <name>HOSTexit</name>
         <value>0xd2b</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-177">
         <name>C$$EXIT</name>
         <value>0xd2a</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-17d">
         <name>__aeabi_fadd</name>
         <value>0x2df</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-17e">
         <name>__addsf3</name>
         <value>0x2df</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-17f">
         <name>__aeabi_fsub</name>
         <value>0x2d5</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-180">
         <name>__subsf3</name>
         <value>0x2d5</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-186">
         <name>__aeabi_fmul</name>
         <value>0x715</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-187">
         <name>__mulsf3</name>
         <value>0x715</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-18d">
         <name>__aeabi_f2iz</name>
         <value>0xb81</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-18e">
         <name>__fixsfsi</name>
         <value>0xb81</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-194">
         <name>__aeabi_i2f</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-195">
         <name>__floatsisf</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-19b">
         <name>__aeabi_memcpy</name>
         <value>0xd1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-19c">
         <name>__aeabi_memcpy4</name>
         <value>0xd1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-19d">
         <name>__aeabi_memcpy8</name>
         <value>0xd1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>__muldsi3</name>
         <value>0xb45</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>memcpy</name>
         <value>0x3ad</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-1be">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c1">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c2">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
