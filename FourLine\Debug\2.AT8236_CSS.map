******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 00:29:36 2025

OUTPUT FILE NAME:   <2.AT8236_CSS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000c1d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000db0  0001f250  R  X
  SRAM                  20200000   00008000  000003ef  00007c11  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000db0   00000db0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000c78   00000c78    r-x .text
  00000d38    00000d38    00000048   00000048    r-- .rodata
  00000d80    00000d80    00000030   00000030    r-- .cinit
20200000    20200000    000001ef   00000000    rw-
  20200000    20200000    00000160   00000000    rw- .bss
  20200160    20200160    0000008f   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000c78     
                  000000c0    00000110     Four_linewalking.o (.text.Four_LineWalking)
                  000001d0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000002d4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000003ac    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000446    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000448    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L1_init)
                  000004d8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L2_init)
                  00000568    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R1_init)
                  000005f8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R2_init)
                  00000688    0000008c     bsp_at8236.o (.text.Motion_Car_Control)
                  00000714    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000007a0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000081c    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000088c    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000008fc    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000095c    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000009bc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000a04    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000a48    00000044     usart.o (.text.UART1_IRQHandler)
                  00000a8c    00000040     delay.o (.text.delay_ms)
                  00000acc    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00000b08    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000b44    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00000b7e    00000002     --HOLE-- [fill = 0]
                  00000b80    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00000bb8    00000034     bsp_at8236.o (.text.init_motor)
                  00000bec    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000c1c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000c44    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000c60    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000c7c    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00000c98    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000cb0    00000016     main.o (.text.main)
                  00000cc6    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000cdc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00000cee    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000d00    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000d10    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  00000d1a    00000002     --HOLE-- [fill = 0]
                  00000d1c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000d24    00000006     libc.a : exit.c.obj (.text:abort)
                  00000d2a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000d2e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000d32    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000d36    00000002     --HOLE-- [fill = 0]

.cinit     0    00000d80    00000030     
                  00000d80    0000000c     (.cinit..data.load) [load image, compression = lzss]
                  00000d8c    0000000c     (__TI_handler_table)
                  00000d98    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000da0    00000010     (__TI_cinit_table)

.rodata    0    00000d38    00000048     
                  00000d38    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00000d42    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00000d4c    00000008     ti_msp_dl_config.o (.rodata.gPWM_L1Config)
                  00000d54    00000008     ti_msp_dl_config.o (.rodata.gPWM_L2Config)
                  00000d5c    00000008     ti_msp_dl_config.o (.rodata.gPWM_R1Config)
                  00000d64    00000008     ti_msp_dl_config.o (.rodata.gPWM_R2Config)
                  00000d6c    00000003     ti_msp_dl_config.o (.rodata.gPWM_L1ClockConfig)
                  00000d6f    00000003     ti_msp_dl_config.o (.rodata.gPWM_L2ClockConfig)
                  00000d72    00000003     ti_msp_dl_config.o (.rodata.gPWM_R1ClockConfig)
                  00000d75    00000003     ti_msp_dl_config.o (.rodata.gPWM_R2ClockConfig)
                  00000d78    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00000d7a    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00000d7c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000160     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_L2Backup)
                  202000bc    000000a0     (.common:gPWM_R1Backup)
                  2020015c    00000004     Four_linewalking.o (.bss.APP_IR_PID_Calc.IRTrack_Integral)

.data      0    20200160    0000008f     UNINITIALIZED
                  20200160    00000080     usart.o (.data.recv0_buff)
                  202001e0    00000004     Four_linewalking.o (.data.IRR_SPEED)
                  202001e4    00000004     Four_linewalking.o (.data.err)
                  202001e8    00000004     Four_linewalking.o (.data.pid_output_IRR)
                  202001ec    00000002     usart.o (.data.recv0_length)
                  202001ee    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1136   68        348    
       startup_mspm0g350x_ticlang.o   6      192       0      
       main.o                         22     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1164   260       348    
                                                              
    .\BSP\
       Four_linewalking.o             272    0         16     
       usart.o                        68     0         131    
       bsp_at8236.o                   192    0         0      
       delay.o                        64     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         596    0         147    
                                                              
    D:/TI/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         456    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         538    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      48        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3186   308       1007   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000da0 records: 2, size/record: 8, table size: 16
	.data: load addr=00000d80, load size=0000000c bytes, run addr=20200160, run size=0000008f bytes, compression=lzss
	.bss: load addr=00000d98, load size=00000008 bytes, run addr=20200000, run size=00000160 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000d8c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000447  ADC0_IRQHandler                 
00000447  ADC1_IRQHandler                 
00000447  AES_IRQHandler                  
00000d2a  C$$EXIT                         
00000447  CANFD0_IRQHandler               
00000447  DAC0_IRQHandler                 
00000d11  DL_Common_delayCycles           
000001d1  DL_Timer_initFourCCPWMMode      
00000c45  DL_Timer_setCaptCompUpdateMethod
00000c99  DL_Timer_setCaptureCompareOutCtl
00000d01  DL_Timer_setCaptureCompareValue 
00000c61  DL_Timer_setClockConfig         
000009bd  DL_UART_init                    
00000cdd  DL_UART_setClockConfig          
00000447  DMA_IRQHandler                  
00000447  Default_Handler                 
000000c1  Four_LineWalking                
00000447  GROUP0_IRQHandler               
00000447  GROUP1_IRQHandler               
00000d2b  HOSTexit                        
00000447  HardFault_Handler               
00000447  I2C0_IRQHandler                 
00000447  I2C1_IRQHandler                 
202001e0  IRR_SPEED                       
00000689  Motion_Car_Control              
00000447  NMI_Handler                     
00000447  PendSV_Handler                  
00000447  RTC_IRQHandler                  
00000d2f  Reset_Handler                   
00000447  SPI0_IRQHandler                 
00000447  SPI1_IRQHandler                 
00000447  SVC_Handler                     
0000081d  SYSCFG_DL_GPIO_init             
00000449  SYSCFG_DL_PWM_L1_init           
000004d9  SYSCFG_DL_PWM_L2_init           
00000569  SYSCFG_DL_PWM_R1_init           
000005f9  SYSCFG_DL_PWM_R2_init           
00000bed  SYSCFG_DL_SYSCTL_init           
00000c7d  SYSCFG_DL_SYSTICK_init          
000008fd  SYSCFG_DL_UART_0_init           
0000095d  SYSCFG_DL_UART_1_init           
00000a05  SYSCFG_DL_init                  
0000088d  SYSCFG_DL_initPower             
00000447  SysTick_Handler                 
00000447  TIMA0_IRQHandler                
00000447  TIMA1_IRQHandler                
00000447  TIMG0_IRQHandler                
00000447  TIMG12_IRQHandler               
00000447  TIMG6_IRQHandler                
00000447  TIMG7_IRQHandler                
00000447  TIMG8_IRQHandler                
00000447  UART0_IRQHandler                
00000a49  UART1_IRQHandler                
00000447  UART2_IRQHandler                
00000447  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000da0  __TI_CINIT_Base                 
00000db0  __TI_CINIT_Limit                
00000db0  __TI_CINIT_Warm                 
00000d8c  __TI_Handler_Table_Base         
00000d98  __TI_Handler_Table_Limit        
00000b09  __TI_auto_init_nobinit_nopinit  
000007a1  __TI_decompress_lzss            
00000cef  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000cc7  __TI_zero_init_nomemset         
000002df  __addsf3                        
00000b81  __aeabi_f2iz                    
000002df  __aeabi_fadd                    
00000715  __aeabi_fmul                    
000002d5  __aeabi_fsub                    
00000acd  __aeabi_i2f                     
00000d1d  __aeabi_memcpy                  
00000d1d  __aeabi_memcpy4                 
00000d1d  __aeabi_memcpy8                 
ffffffff  __binit__                       
00000b81  __fixsfsi                       
00000acd  __floatsisf                     
UNDEFED   __mpu_init                      
00000b45  __muldsi3                       
00000715  __mulsf3                        
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000002d5  __subsf3                        
00000c1d  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000d33  _system_pre_init                
00000d25  abort                           
ffffffff  binit                           
00000a8d  delay_ms                        
202001e4  err                             
20200000  gPWM_L2Backup                   
202000bc  gPWM_R1Backup                   
00000bb9  init_motor                      
00000000  interruptVectors                
00000cb1  main                            
000003ad  memcpy                          
202001e8  pid_output_IRR                  
20200160  recv0_buff                      
202001ee  recv0_flag                      
202001ec  recv0_length                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  Four_LineWalking                
000001d1  DL_Timer_initFourCCPWMMode      
00000200  __STACK_SIZE                    
000002d5  __aeabi_fsub                    
000002d5  __subsf3                        
000002df  __addsf3                        
000002df  __aeabi_fadd                    
000003ad  memcpy                          
00000447  ADC0_IRQHandler                 
00000447  ADC1_IRQHandler                 
00000447  AES_IRQHandler                  
00000447  CANFD0_IRQHandler               
00000447  DAC0_IRQHandler                 
00000447  DMA_IRQHandler                  
00000447  Default_Handler                 
00000447  GROUP0_IRQHandler               
00000447  GROUP1_IRQHandler               
00000447  HardFault_Handler               
00000447  I2C0_IRQHandler                 
00000447  I2C1_IRQHandler                 
00000447  NMI_Handler                     
00000447  PendSV_Handler                  
00000447  RTC_IRQHandler                  
00000447  SPI0_IRQHandler                 
00000447  SPI1_IRQHandler                 
00000447  SVC_Handler                     
00000447  SysTick_Handler                 
00000447  TIMA0_IRQHandler                
00000447  TIMA1_IRQHandler                
00000447  TIMG0_IRQHandler                
00000447  TIMG12_IRQHandler               
00000447  TIMG6_IRQHandler                
00000447  TIMG7_IRQHandler                
00000447  TIMG8_IRQHandler                
00000447  UART0_IRQHandler                
00000447  UART2_IRQHandler                
00000447  UART3_IRQHandler                
00000449  SYSCFG_DL_PWM_L1_init           
000004d9  SYSCFG_DL_PWM_L2_init           
00000569  SYSCFG_DL_PWM_R1_init           
000005f9  SYSCFG_DL_PWM_R2_init           
00000689  Motion_Car_Control              
00000715  __aeabi_fmul                    
00000715  __mulsf3                        
000007a1  __TI_decompress_lzss            
0000081d  SYSCFG_DL_GPIO_init             
0000088d  SYSCFG_DL_initPower             
000008fd  SYSCFG_DL_UART_0_init           
0000095d  SYSCFG_DL_UART_1_init           
000009bd  DL_UART_init                    
00000a05  SYSCFG_DL_init                  
00000a49  UART1_IRQHandler                
00000a8d  delay_ms                        
00000acd  __aeabi_i2f                     
00000acd  __floatsisf                     
00000b09  __TI_auto_init_nobinit_nopinit  
00000b45  __muldsi3                       
00000b81  __aeabi_f2iz                    
00000b81  __fixsfsi                       
00000bb9  init_motor                      
00000bed  SYSCFG_DL_SYSCTL_init           
00000c1d  _c_int00_noargs                 
00000c45  DL_Timer_setCaptCompUpdateMethod
00000c61  DL_Timer_setClockConfig         
00000c7d  SYSCFG_DL_SYSTICK_init          
00000c99  DL_Timer_setCaptureCompareOutCtl
00000cb1  main                            
00000cc7  __TI_zero_init_nomemset         
00000cdd  DL_UART_setClockConfig          
00000cef  __TI_decompress_none            
00000d01  DL_Timer_setCaptureCompareValue 
00000d11  DL_Common_delayCycles           
00000d1d  __aeabi_memcpy                  
00000d1d  __aeabi_memcpy4                 
00000d1d  __aeabi_memcpy8                 
00000d25  abort                           
00000d2a  C$$EXIT                         
00000d2b  HOSTexit                        
00000d2f  Reset_Handler                   
00000d33  _system_pre_init                
00000d8c  __TI_Handler_Table_Base         
00000d98  __TI_Handler_Table_Limit        
00000da0  __TI_CINIT_Base                 
00000db0  __TI_CINIT_Limit                
00000db0  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_L2Backup                   
202000bc  gPWM_R1Backup                   
20200160  recv0_buff                      
202001e0  IRR_SPEED                       
202001e4  err                             
202001e8  pid_output_IRR                  
202001ec  recv0_length                    
202001ee  recv0_flag                      
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[117 symbols]
