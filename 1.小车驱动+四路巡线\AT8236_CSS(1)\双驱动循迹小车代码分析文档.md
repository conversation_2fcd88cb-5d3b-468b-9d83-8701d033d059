# 双驱动循迹小车代码分析文档

## 项目概述

本项目是基于TI MSPM0G350X微控制器的双驱动循迹小车系统，使用Code Composer Studio (CCS)开发环境。项目实现了双电机驱动控制和简化的直线循迹功能。

## 硬件平台信息

- **微控制器**: MSPM0G350X
- **封装**: LQFP-64(PM)
- **开发环境**: Code Composer Studio (CCS)
- **SDK版本**: mspm0_sdk@2.05.01.00
- **工具版本**: 1.24.0+4110

## 系统架构

### 模块结构
```
双驱动循迹小车系统
├── 主控制模块 (main.c)
├── 车辆控制模块 (car_control.c/h)
├── 电机驱动模块 (bsp_at8236.c/h)
├── 循迹模块 (Four_linewalking.c/h)
├── 延时模块 (delay.c/h)
├── 串口模块 (usart.c/h)
└── 系统配置 (empty.syscfg)
```

## 详细模块分析

### 1. 主控制模块 (main.c)

#### 功能概述
- 系统初始化和状态管理
- 实现启动测试和循迹模式切换
- 整体程序流程控制

#### 核心功能
```c
// 系统状态定义
typedef enum {
    SYSTEM_INIT = 0,      // 系统初始化
    SYSTEM_TEST,          // 测试运行阶段
    SYSTEM_LINE_FOLLOW    // 循迹运行阶段
} system_state_t;
```

#### 运行流程
1. **系统初始化**: 配置硬件、初始化各模块
2. **测试运行**: 前进5秒后停止，验证系统正常
3. **循迹模式**: 进入直线循迹状态

### 2. 车辆控制模块 (car_control.c/h)

#### 功能概述
提供高级车辆运动控制接口，封装底层电机控制。

#### 主要函数
- `car_init()`: 车辆初始化
- `car_forward(speed)`: 前进控制
- `car_stop()`: 停止控制
- `car_set_speed()`: 精确速度控制

#### 控制参数
```c
#define CAR_DEFAULT_SPEED    100    // 默认速度
#define CAR_TURN_SPEED       80     // 转弯速度
#define CAR_MAX_SPEED        1000   // 最大速度
```

### 3. 电机驱动模块 (bsp_at8236.c/h)

#### 功能概述
双驱动电机控制，只保留L1和R1两个电机。

#### 硬件配置
| 电机 | PWM定时器 | 引脚配置 | 功能 |
|------|-----------|----------|------|
| L1   | TIMG0     | PA12, PA13 | 左侧电机 |
| R1   | TIMG6     | PA21, PA22 | 右侧电机 |

#### 控制方式
- **正转**: 通道0输出PWM，通道1输出0
- **反转**: 通道0输出0，通道1输出PWM
- **停止**: 两通道都输出0

### 4. 循迹模块 (Four_linewalking.c/h)

#### 功能概述
简化的直线循迹模块，去除复杂转弯逻辑。

#### 传感器配置
```c
// 传感器引脚映射
X1 -> PA24 (L2) // 左侧外传感器
X2 -> PA25 (L1) // 左侧内传感器
X3 -> PA26 (R1) // 右侧内传感器
X4 -> PA27 (R2) // 右侧外传感器
```

#### 循迹逻辑
1. **检测黑线**: 任一传感器检测到LOW信号
2. **直线行驶**: 中间两传感器都检测到黑线
3. **微调控制**: 单侧传感器检测到时进行差速调整
4. **停止条件**: 无传感器检测到黑线时停止

### 5. 系统配置模块 (empty.syscfg)

#### PWM配置
```javascript
// 左侧电机PWM (PWM_L1)
PWM1.clockDivider = 8
PWM1.clockPrescale = 40
// 右侧电机PWM (PWM_R1)  
PWM2.clockDivider = 8
PWM2.clockPrescale = 40
```

#### 时钟计算
- 系统时钟: 32MHz
- PWM频率: 32MHz / 8 / 40 = 100kHz
- 提供良好的控制精度

## 系统工作流程

### 启动流程
1. **硬件初始化**: SYSCFG_DL_init()
2. **模块初始化**: 串口、车辆控制、循迹模块
3. **测试运行**: 前进5秒验证系统
4. **进入循迹**: 开始直线循迹模式

### 循迹流程
1. **读取传感器**: 获取四路传感器状态
2. **判断黑线**: 检测是否有黑线存在
3. **运动控制**: 根据传感器状态调整运动
4. **循环执行**: 持续监测和控制

## 技术特点

### 1. 简化设计
- 双驱动替代四驱动，降低复杂度
- 直线循迹替代复杂路径规划
- 模块化设计便于维护

### 2. 实时响应
- 硬件PWM生成，波形稳定
- 简单控制逻辑，响应快速
- 10ms循环周期，实时性好

### 3. 调试友好
- 串口输出调试信息
- 分阶段运行便于测试
- 清晰的状态管理

## 使用说明

### 编译和烧录
1. 使用CCS打开项目
2. 编译生成hex文件
3. 烧录到MSPM0G350X

### 运行测试
1. **上电启动**: 系统自动初始化
2. **测试阶段**: 小车前进5秒后停止
3. **循迹阶段**: 放置在白底黑线环境
4. **正常运行**: 沿黑线匀速前进，无线停止

### 调试输出
通过串口可以查看：
- 系统初始化信息
- 运行状态切换
- 传感器状态（可选）

## 扩展建议

### 功能扩展
1. **转弯功能**: 添加左右转弯逻辑
2. **速度调节**: 实现动态速度控制
3. **路径记忆**: 添加路径学习功能

### 性能优化
1. **PID控制**: 实现更精确的循迹
2. **传感器滤波**: 提高检测稳定性
3. **电机保护**: 添加过流保护

## 总结

本双驱动循迹小车系统具有以下特点：

1. **结构简化**: 双驱动设计降低了硬件复杂度
2. **功能专一**: 专注于直线循迹，稳定可靠
3. **易于调试**: 分阶段运行便于问题定位
4. **扩展性好**: 模块化设计便于功能扩展

系统适用于循迹小车的基础应用，为后续功能扩展提供了良好的基础架构。
