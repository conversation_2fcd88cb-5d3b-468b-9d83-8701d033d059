#include "car_control.h"

// 全局变量
static car_state_t current_state = CAR_STOP;

/**
 * @brief 车辆初始化
 */
void car_init(void)
{
    init_motor();           // 初始化电机PWM
    car_stop();            // 初始状态为停止
    current_state = CAR_STOP;
}

/**
 * @brief 停止车辆
 */
void car_stop(void)
{
    motor_stop();
    current_state = CAR_STOP;
}

/**
 * @brief 车辆前进
 * @param speed 速度值 (0-1000)
 */
void car_forward(uint16_t speed)
{
    if(speed > CAR_MAX_SPEED) speed = CAR_MAX_SPEED;
    
    motor_forward(speed);
    current_state = CAR_FORWARD;
}

/**
 * @brief 车辆后退
 * @param speed 速度值 (0-1000)
 */
void car_backward(uint16_t speed)
{
    if(speed > CAR_MAX_SPEED) speed = CAR_MAX_SPEED;
    
    motor_backward(speed);
    current_state = CAR_BACKWARD;
}

/**
 * @brief 车辆左转
 * @param speed 速度值 (0-1000)
 */
void car_turn_left(uint16_t speed)
{
    if(speed > CAR_MAX_SPEED) speed = CAR_MAX_SPEED;
    
    motor_turn_left(speed);
    current_state = CAR_TURN_LEFT;
}

/**
 * @brief 车辆右转
 * @param speed 速度值 (0-1000)
 */
void car_turn_right(uint16_t speed)
{
    if(speed > CAR_MAX_SPEED) speed = CAR_MAX_SPEED;
    
    motor_turn_right(speed);
    current_state = CAR_TURN_RIGHT;
}

/**
 * @brief 设置左右轮速度和方向
 * @param left_speed 左轮速度 (0-1000)
 * @param right_speed 右轮速度 (0-1000)
 * @param left_dir 左轮方向 (1=正转, 0=反转)
 * @param right_dir 右轮方向 (1=正转, 0=反转)
 */
void car_set_speed(uint16_t left_speed, uint16_t right_speed, 
                   uint8_t left_dir, uint8_t right_dir)
{
    if(left_speed > CAR_MAX_SPEED) left_speed = CAR_MAX_SPEED;
    if(right_speed > CAR_MAX_SPEED) right_speed = CAR_MAX_SPEED;
    
    L1_control(left_speed, left_dir);
    R1_control(right_speed, right_dir);
}

/**
 * @brief 测试运行 - 转5秒后停止
 */
void car_test_run(void)
{
    // 前进5秒
    car_forward(CAR_DEFAULT_SPEED);
    delay_ms(5000);  // 延时5秒
    
    // 停止
    car_stop();
}

/**
 * @brief 获取当前车辆状态
 * @return 当前状态
 */
car_state_t car_get_state(void)
{
    return current_state;
}

/**
 * @brief 设置车辆状态
 * @param state 要设置的状态
 */
void car_set_state(car_state_t state)
{
    current_state = state;
    
    switch(state)
    {
        case CAR_STOP:
            car_stop();
            break;
        case CAR_FORWARD:
            car_forward(CAR_DEFAULT_SPEED);
            break;
        case CAR_BACKWARD:
            car_backward(CAR_DEFAULT_SPEED);
            break;
        case CAR_TURN_LEFT:
            car_turn_left(CAR_TURN_SPEED);
            break;
        case CAR_TURN_RIGHT:
            car_turn_right(CAR_TURN_SPEED);
            break;
        default:
            car_stop();
            break;
    }
}
