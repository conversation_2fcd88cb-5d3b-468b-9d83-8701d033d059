#ifndef __FOUR_LINEWALKING_H__
#define __FOUR_LINEWALKING_H__
#include "ti_msp_dl_config.h"
#include "delay.h"
#include "bsp_at8236.h"

/*

	从车辆后部向前看： 从左到右巡线传感器的顺序为  L2  L1  R1  R2
	Looking forward from the rear of the vehicle: The order of the line-following sensors from left to right is L2  L1  R1  R2

*/

#define Sensor_PORT GPIOA
#define Sensor_X1_PIN    DL_GPIO_PIN_24  //PA24
#define Sensor_X2_PIN    DL_GPIO_PIN_25  //PA25
#define Sensor_X3_PIN    DL_GPIO_PIN_26  //PA26
#define Sensor_X4_PIN    DL_GPIO_PIN_27  //PA27


#define LineWalk_L1_IN		( ( ( DL_GPIO_readPins(Sensor_PORT,Sensor_X2_PIN) & Sensor_X2_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_L2_IN		( ( ( DL_GPIO_readPins(Sensor_PORT,Sensor_X1_PIN) & Sensor_X1_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_R1_IN		( ( ( DL_GPIO_readPins(Sensor_PORT,Sensor_X3_PIN) & Sensor_X3_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_R2_IN		( ( ( DL_GPIO_readPins(Sensor_PORT,Sensor_X4_PIN) & Sensor_X4_PIN ) > 0 ) ? 1 : 0 )

#define LOW		(0)
#define HIGH	(1)

extern int Left_rui;
extern int Right_rui;

void Four_GetLineWalking(int *LineL1, int *LineL2, int *LineR1, int *LineR2);
void Four_LineWalking(void);


#endif
