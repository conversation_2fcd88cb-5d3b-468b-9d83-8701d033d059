Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    bsp_uart.o(.text.bsp_uart_init) refers to lq_usart.o(.text.uart_init) for uart_init
    bsp_uart.o(.text.bsp_uart_init) refers to bsp_uart.o(.bss.OMV) for OMV
    bsp_uart.o(.ARM.exidx.text.bsp_uart_init) refers to bsp_uart.o(.text.bsp_uart_init) for [Anonymous Symbol]
    bsp_uart.o(.text.Test_uart) refers to bsp_uart.o(.text.bsp_uart_init) for bsp_uart_init
    bsp_uart.o(.text.Test_uart) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    bsp_uart.o(.text.Test_uart) refers to printfa.o(i.__0sprintf) for sprintf
    bsp_uart.o(.text.Test_uart) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    bsp_uart.o(.text.Test_uart) refers to f2d.o(.text) for __aeabi_f2d
    bsp_uart.o(.text.Test_uart) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    bsp_uart.o(.text.Test_uart) refers to bsp_uart.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_uart.o(.text.Test_uart) refers to bsp_uart.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    bsp_uart.o(.text.Test_uart) refers to include.o(.text.delay_ms) for delay_ms
    bsp_uart.o(.text.Test_uart) refers to bsp_uart.o(.bss.OMV) for OMV
    bsp_uart.o(.text.Test_uart) refers to bsp_uart.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_uart.o(.text.Test_uart) refers to lq_oled.o(.bss.txt) for txt
    bsp_uart.o(.ARM.exidx.text.Test_uart) refers to bsp_uart.o(.text.Test_uart) for [Anonymous Symbol]
    bsp_uart.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bsp_uart.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bsp_uart.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to bsp_uart.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    bsp_uart.o(.text.Uart_getdata) refers to sscanf.o(.text) for sscanf
    bsp_uart.o(.text.Uart_getdata) refers to strstr.o(.text) for strstr
    bsp_uart.o(.text.Uart_getdata) refers to memseta.o(.text) for __aeabi_memclr
    bsp_uart.o(.text.Uart_getdata) refers to lq_usart.o(.bss.uart0_flag) for uart0_flag
    bsp_uart.o(.text.Uart_getdata) refers to lq_usart.o(.bss.uart0_Buffer) for uart0_Buffer
    bsp_uart.o(.text.Uart_getdata) refers to bsp_uart.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_uart.o(.text.Uart_getdata) refers to bsp_uart.o(.bss.OMV) for OMV
    bsp_uart.o(.ARM.exidx.text.Uart_getdata) refers to bsp_uart.o(.text.Uart_getdata) for [Anonymous Symbol]
    empty.o(.text.PlacePID_Control) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.PlacePID_Control) refers to fadd.o(.text) for __aeabi_fsub
    empty.o(.text.PlacePID_Control) refers to dadd.o(.text) for __aeabi_dsub
    empty.o(.text.PlacePID_Control) refers to dmul.o(.text) for __aeabi_dmul
    empty.o(.text.PlacePID_Control) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.ARM.exidx.text.PlacePID_Control) refers to empty.o(.text.PlacePID_Control) for [Anonymous Symbol]
    empty.o(.text.PID_Realize) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.PID_Realize) refers to fadd.o(.text) for __aeabi_fsub
    empty.o(.text.PID_Realize) refers to dadd.o(.text) for __aeabi_dsub
    empty.o(.text.PID_Realize) refers to dmul.o(.text) for __aeabi_dmul
    empty.o(.text.PID_Realize) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.ARM.exidx.text.PID_Realize) refers to empty.o(.text.PID_Realize) for [Anonymous Symbol]
    empty.o(.text.range_protect) refers to dcmplt.o(.text) for __aeabi_dcmplt
    empty.o(.text.range_protect) refers to dcmpgt.o(.text) for __aeabi_dcmpgt
    empty.o(.ARM.exidx.text.range_protect) refers to empty.o(.text.range_protect) for [Anonymous Symbol]
    empty.o(.text.Adc_Normalize) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.Adc_Normalize) refers to fdiv.o(.text) for __aeabi_fdiv
    empty.o(.text.Adc_Normalize) refers to fmul.o(.text) for __aeabi_fmul
    empty.o(.text.Adc_Normalize) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.Adc_Normalize) refers to dcmplt.o(.text) for __aeabi_dcmplt
    empty.o(.text.Adc_Normalize) refers to dcmpge.o(.text) for __aeabi_dcmpge
    empty.o(.text.Adc_Normalize) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.ARM.exidx.text.Adc_Normalize) refers to empty.o(.text.Adc_Normalize) for [Anonymous Symbol]
    empty.o(.text.AD_Get) refers to empty.o(.text.Adc_Normalize) for Adc_Normalize
    empty.o(.text.AD_Get) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.AD_Get) refers to lq_tracking.o(.bss.LQ_Tracking_Value) for LQ_Tracking_Value
    empty.o(.text.AD_Get) refers to empty.o(.bss.Adc_Value) for Adc_Value
    empty.o(.text.AD_Get) refers to empty.o(.data.Adc_Min) for Adc_Min
    empty.o(.text.AD_Get) refers to empty.o(.data.Adc_Max) for Adc_Max
    empty.o(.ARM.exidx.text.AD_Get) refers to empty.o(.text.AD_Get) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.Angle_calculate) refers to empty.o(.text.Angle_calculate) for [Anonymous Symbol]
    empty.o(.text.Dir_Control) refers to empty.o(.text.AD_Get) for AD_Get
    empty.o(.text.Dir_Control) refers to dflti.o(.text) for __aeabi_i2d
    empty.o(.text.Dir_Control) refers to dmul.o(.text) for __aeabi_dmul
    empty.o(.text.Dir_Control) refers to dadd.o(.text) for __aeabi_dadd
    empty.o(.text.Dir_Control) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.text.Dir_Control) refers to fadd.o(.text) for __aeabi_fsub
    empty.o(.text.Dir_Control) refers to empty.o(.text.PlacePID_Control) for PlacePID_Control
    empty.o(.text.Dir_Control) refers to empty.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    empty.o(.text.Dir_Control) refers to ddiv.o(.text) for __aeabi_ddiv
    empty.o(.text.Dir_Control) refers to dfltui.o(.text) for __aeabi_ui2d
    empty.o(.text.Dir_Control) refers to dfixui.o(.text) for __aeabi_d2uiz
    empty.o(.text.Dir_Control) refers to empty.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    empty.o(.text.Dir_Control) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.Dir_Control) refers to empty.o(.text.range_protect) for range_protect
    empty.o(.text.Dir_Control) refers to fcmplt.o(.text) for __aeabi_fcmplt
    empty.o(.text.Dir_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.Dir_Control) refers to lq_servo.o(.text.Servo_Ctrl) for Servo_Ctrl
    empty.o(.text.Dir_Control) refers to empty.o(.bss.Adc_error) for Adc_error
    empty.o(.text.Dir_Control) refers to empty.o(.bss.L_error) for L_error
    empty.o(.text.Dir_Control) refers to empty.o(.bss.R_error) for R_error
    empty.o(.text.Dir_Control) refers to empty.o(.bss.eleValue) for eleValue
    empty.o(.text.Dir_Control) refers to empty.o(.bss.elemid) for elemid
    empty.o(.text.Dir_Control) refers to empty.o(.bss.Turn_PID_ele) for Turn_PID_ele
    empty.o(.text.Dir_Control) refers to empty.o(.data.Turn_ele) for Turn_ele
    empty.o(.text.Dir_Control) refers to empty.o(.bss.eleOut) for eleOut
    empty.o(.text.Dir_Control) refers to empty.o(.bss.turn_rrr) for turn_rrr
    empty.o(.text.Dir_Control) refers to empty.o(.bss.Adc_Value) for Adc_Value
    empty.o(.text.Dir_Control) refers to lq_encoder.o(.bss.LQ_encoder_L) for LQ_encoder_L
    empty.o(.text.Dir_Control) refers to lq_encoder.o(.bss.LQ_encoder_R) for LQ_encoder_R
    empty.o(.text.Dir_Control) refers to empty.o(.bss.jibu) for jibu
    empty.o(.text.Dir_Control) refers to empty.o(.data.run) for run
    empty.o(.text.Dir_Control) refers to empty.o(.bss.High_Speed) for High_Speed
    empty.o(.text.Dir_Control) refers to empty.o(.bss.Left_High_Speed) for Left_High_Speed
    empty.o(.text.Dir_Control) refers to empty.o(.bss.Right_High_Speed) for Right_High_Speed
    empty.o(.text.Dir_Control) refers to bsp_uart.o(.bss.OMV) for OMV
    empty.o(.text.Dir_Control) refers to empty.o(.bss.UP_LOW_Value) for UP_LOW_Value
    empty.o(.text.Dir_Control) refers to empty.o(.bss.LEFT_RIGHT_Value) for LEFT_RIGHT_Value
    empty.o(.text.Dir_Control) refers to empty.o(.data.UP_LOW_Servo_Out) for UP_LOW_Servo_Out
    empty.o(.text.Dir_Control) refers to empty.o(.data.LEFT_RIGHT_Servo_Out) for LEFT_RIGHT_Servo_Out
    empty.o(.text.Dir_Control) refers to empty.o(.bss.last_up_low) for last_up_low
    empty.o(.text.Dir_Control) refers to empty.o(.bss.last_left_right) for last_left_right
    empty.o(.ARM.exidx.text.Dir_Control) refers to empty.o(.text.Dir_Control) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_GPIO_setPins) refers to empty.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to empty.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    empty.o(.text.Motor_Control) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.Motor_Control) refers to empty.o(.text.PID_Realize) for PID_Realize
    empty.o(.text.Motor_Control) refers to fadd.o(.text) for __aeabi_fadd
    empty.o(.text.Motor_Control) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.Motor_Control) refers to empty.o(.text.range_protect) for range_protect
    empty.o(.text.Motor_Control) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.text.Motor_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.Motor_Control) refers to lq_motor.o(.text.Motor_Ctrl) for Motor_Ctrl
    empty.o(.text.Motor_Control) refers to lq_encoder.o(.bss.LQ_encoder_L) for LQ_encoder_L
    empty.o(.text.Motor_Control) refers to empty.o(.bss.Left_High_Speed) for Left_High_Speed
    empty.o(.text.Motor_Control) refers to empty.o(.bss.Left_MOTOR_PID) for Left_MOTOR_PID
    empty.o(.text.Motor_Control) refers to empty.o(.data.Left_MOTOR) for Left_MOTOR
    empty.o(.text.Motor_Control) refers to empty.o(.bss.LEFT_MOTOR_Duty) for LEFT_MOTOR_Duty
    empty.o(.text.Motor_Control) refers to lq_encoder.o(.bss.LQ_encoder_R) for LQ_encoder_R
    empty.o(.text.Motor_Control) refers to empty.o(.bss.Right_High_Speed) for Right_High_Speed
    empty.o(.text.Motor_Control) refers to empty.o(.bss.Right_MOTOR_PID) for Right_MOTOR_PID
    empty.o(.text.Motor_Control) refers to empty.o(.data.Right_MOTOR) for Right_MOTOR
    empty.o(.text.Motor_Control) refers to empty.o(.bss.RIGHT_MOTOR_Duty) for RIGHT_MOTOR_Duty
    empty.o(.ARM.exidx.text.Motor_Control) refers to empty.o(.text.Motor_Control) for [Anonymous Symbol]
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to include.o(.text.delay_ms) for delay_ms
    empty.o(.text.main) refers to lq_encoder.o(.text.Encoder_Init) for Encoder_Init
    empty.o(.text.main) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    empty.o(.text.main) refers to lq_tracking.o(.text.Tracking_Adc_Init) for Tracking_Adc_Init
    empty.o(.text.main) refers to bsp_uart.o(.text.bsp_uart_init) for bsp_uart_init
    empty.o(.text.main) refers to printfa.o(i.__0sprintf) for sprintf
    empty.o(.text.main) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    empty.o(.text.main) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.main) refers to lq_tracking.o(.text.Tracking_Value_Acquire) for Tracking_Value_Acquire
    empty.o(.text.main) refers to empty.o(.text.AD_Get) for AD_Get
    empty.o(.text.main) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    empty.o(.text.main) refers to bsp_uart.o(.bss.OMV) for OMV
    empty.o(.text.main) refers to lq_oled.o(.bss.txt) for txt
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.main) refers to empty.o(.data.UP_LOW_Servo_Out) for UP_LOW_Servo_Out
    empty.o(.text.main) refers to empty.o(.data.LEFT_RIGHT_Servo_Out) for LEFT_RIGHT_Servo_Out
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.TIMG6_IRQHandler) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    empty.o(.text.TIMG6_IRQHandler) refers to empty.o(.text.Dir_Control) for Dir_Control
    empty.o(.text.TIMG6_IRQHandler) refers to empty.o(.text.Motor_Control) for Motor_Control
    empty.o(.text.TIMG6_IRQHandler) refers to lq_key.o(.text.Key_Control) for Key_Control
    empty.o(.text.TIMG6_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_L_Last) for LQ_encoder_L_Last
    empty.o(.text.TIMG6_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_L) for LQ_encoder_L
    empty.o(.text.TIMG6_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_R_Last) for LQ_encoder_R_Last
    empty.o(.text.TIMG6_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_R) for LQ_encoder_R
    empty.o(.ARM.exidx.text.TIMG6_IRQHandler) refers to empty.o(.text.TIMG6_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    include.o(.ARM.exidx.text.delay_us) refers to include.o(.text.delay_us) for [Anonymous Symbol]
    include.o(.text.delay_ms) refers to include.o(.text.delay_us) for delay_us
    include.o(.ARM.exidx.text.delay_ms) refers to include.o(.text.delay_ms) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) for SYSCFG_DL_PWM_Motor_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) for SYSCFG_DL_PWM_Servo_A0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) for SYSCFG_DL_PWM_Servo_G0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) for SYSCFG_DL_ADC_Tracking_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_MotorBackup) for gPWM_MotorBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_Servo_A0Backup) for gPWM_Servo_A0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for DL_ADC12_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for DL_ADC12_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for DL_GPIO_initDigitalOutputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MotorClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MotorConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Motor_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_Servo_A0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_Servo_A0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Servo_A0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_Servo_G0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_Servo_G0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Servo_G0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for DL_ADC12_initSingleSample
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for DL_ADC12_configConversionMem
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for DL_ADC12_setSampleTime0
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for DL_ADC12_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for DL_ADC12_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.rodata.gADC_TrackingClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_Tracking_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MotorBackup) for gPWM_MotorBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_Servo_A0Backup) for gPWM_Servo_A0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MotorBackup) for gPWM_MotorBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_Servo_A0Backup) for gPWM_Servo_A0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to lq_encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to lq_tracking.o(.text.ADC0_IRQHandler) for ADC0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to lq_usart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG6_IRQHandler) for TIMG6_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lq_gpio.o(.text.LQ_GPIO_readPins) refers to lq_gpio.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    lq_gpio.o(.ARM.exidx.text.LQ_GPIO_readPins) refers to lq_gpio.o(.text.LQ_GPIO_readPins) for [Anonymous Symbol]
    lq_gpio.o(.ARM.exidx.text.DL_GPIO_readPins) refers to lq_gpio.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    lq_gpio.o(.text.LQ_Test_LED) refers to lq_gpio.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    lq_gpio.o(.text.LQ_Test_LED) refers to include.o(.text.delay_ms) for delay_ms
    lq_gpio.o(.ARM.exidx.text.LQ_Test_LED) refers to lq_gpio.o(.text.LQ_Test_LED) for [Anonymous Symbol]
    lq_gpio.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to lq_gpio.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    lq_key.o(.text.Key_Control) refers to lq_gpio.o(.text.LQ_GPIO_readPins) for LQ_GPIO_readPins
    lq_key.o(.text.Key_Control) refers to fadd.o(.text) for __aeabi_fadd
    lq_key.o(.text.Key_Control) refers to lq_key.o(.bss.LQ_key1_flag) for LQ_key1_flag
    lq_key.o(.text.Key_Control) refers to empty.o(.data.LEFT_RIGHT_Servo_Out) for LEFT_RIGHT_Servo_Out
    lq_key.o(.text.Key_Control) refers to lq_key.o(.bss.LQ_key2_flag) for LQ_key2_flag
    lq_key.o(.text.Key_Control) refers to empty.o(.data.UP_LOW_Servo_Out) for UP_LOW_Servo_Out
    lq_key.o(.text.Key_Control) refers to lq_key.o(.bss.LQ_key3_flag) for LQ_key3_flag
    lq_key.o(.ARM.exidx.text.Key_Control) refers to lq_key.o(.text.Key_Control) for [Anonymous Symbol]
    lq_key.o(.text.Switch_Control) refers to lq_gpio.o(.text.LQ_GPIO_readPins) for LQ_GPIO_readPins
    lq_key.o(.ARM.exidx.text.Switch_Control) refers to lq_key.o(.text.Switch_Control) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_WR_Byte) refers to lq_oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lq_oled.o(.text.OLED_WR_Byte) refers to lq_oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lq_oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to lq_oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    lq_oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to lq_oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    lq_oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to lq_oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ColorTurn) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.ARM.exidx.text.OLED_ColorTurn) refers to lq_oled.o(.text.OLED_ColorTurn) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DisplayTurn) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.ARM.exidx.text.OLED_DisplayTurn) refers to lq_oled.o(.text.OLED_DisplayTurn) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DisPlay_On) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.ARM.exidx.text.OLED_DisPlay_On) refers to lq_oled.o(.text.OLED_DisPlay_On) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DisPlay_Off) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.ARM.exidx.text.OLED_DisPlay_Off) refers to lq_oled.o(.text.OLED_DisPlay_Off) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_Refresh) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.text.OLED_Refresh) refers to lq_oled.o(.bss.OLED_GRAM) for OLED_GRAM
    lq_oled.o(.ARM.exidx.text.OLED_Refresh) refers to lq_oled.o(.text.OLED_Refresh) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_Clear) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_oled.o(.text.OLED_Clear) refers to lq_oled.o(.bss.OLED_GRAM) for OLED_GRAM
    lq_oled.o(.ARM.exidx.text.OLED_Clear) refers to lq_oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DrawPoint) refers to lq_oled.o(.bss.OLED_GRAM) for OLED_GRAM
    lq_oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to lq_oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DrawLine) refers to lq_oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    lq_oled.o(.ARM.exidx.text.OLED_DrawLine) refers to lq_oled.o(.text.OLED_DrawLine) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_DrawCircle) refers to lq_oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    lq_oled.o(.ARM.exidx.text.OLED_DrawCircle) refers to lq_oled.o(.text.OLED_DrawCircle) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ShowChar) refers to lq_oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    lq_oled.o(.text.OLED_ShowChar) refers to lq_oledfont.o(.rodata.asc2_1206) for asc2_1206
    lq_oled.o(.text.OLED_ShowChar) refers to lq_oledfont.o(.rodata.asc2_0806) for asc2_0806
    lq_oled.o(.ARM.exidx.text.OLED_ShowChar) refers to lq_oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ShowString) refers to lq_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    lq_oled.o(.ARM.exidx.text.OLED_ShowString) refers to lq_oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    lq_oled.o(.ARM.exidx.text.OLED_Pow) refers to lq_oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ShowNum) refers to lq_oled.o(.text.OLED_Pow) for OLED_Pow
    lq_oled.o(.text.OLED_ShowNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    lq_oled.o(.text.OLED_ShowNum) refers to lq_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    lq_oled.o(.ARM.exidx.text.OLED_ShowNum) refers to lq_oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ShowChinese) refers to lq_oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    lq_oled.o(.text.OLED_ShowChinese) refers to lq_oledfont.o(.rodata.Hzk1) for Hzk1
    lq_oled.o(.ARM.exidx.text.OLED_ShowChinese) refers to lq_oled.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ScrollDisplay) refers to lq_oled.o(.text.OLED_ShowChinese) for OLED_ShowChinese
    lq_oled.o(.text.OLED_ScrollDisplay) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_oled.o(.text.OLED_ScrollDisplay) refers to lq_oled.o(.bss.OLED_GRAM) for OLED_GRAM
    lq_oled.o(.ARM.exidx.text.OLED_ScrollDisplay) refers to lq_oled.o(.text.OLED_ScrollDisplay) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_ShowPicture) refers to lq_oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    lq_oled.o(.ARM.exidx.text.OLED_ShowPicture) refers to lq_oled.o(.text.OLED_ShowPicture) for [Anonymous Symbol]
    lq_oled.o(.text.OLED_Init) refers to lq_oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lq_oled.o(.text.OLED_Init) refers to include.o(.text.delay_ms) for delay_ms
    lq_oled.o(.text.OLED_Init) refers to lq_oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lq_oled.o(.text.OLED_Init) refers to lq_oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    lq_oled.o(.text.OLED_Init) refers to lq_oled.o(.text.OLED_Clear) for OLED_Clear
    lq_oled.o(.ARM.exidx.text.OLED_Init) refers to lq_oled.o(.text.OLED_Init) for [Anonymous Symbol]
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_oled.o(.text.LQ_Test_OLED) refers to printfa.o(i.__0sprintf) for sprintf
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_oled.o(.text.LQ_Test_OLED) refers to f2d.o(.text) for __aeabi_f2d
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.text.OLED_ShowChinese) for OLED_ShowChinese
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    lq_oled.o(.text.LQ_Test_OLED) refers to include.o(.text.delay_ms) for delay_ms
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.data.oled_data1) for oled_data1
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.bss.txt) for txt
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.data.oled_data2) for oled_data2
    lq_oled.o(.text.LQ_Test_OLED) refers to lq_oled.o(.data.oled_data3) for oled_data3
    lq_oled.o(.ARM.exidx.text.LQ_Test_OLED) refers to lq_oled.o(.text.LQ_Test_OLED) for [Anonymous Symbol]
    lq_oled.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to lq_oled.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    lq_encoder.o(.text.Encoder_Init) refers to lq_encoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    lq_encoder.o(.text.Encoder_Init) refers to lq_encoder.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    lq_encoder.o(.ARM.exidx.text.Encoder_Init) refers to lq_encoder.o(.text.Encoder_Init) for [Anonymous Symbol]
    lq_encoder.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to lq_encoder.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    lq_encoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to lq_encoder.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.bss.GROUP1_IRQHandler.GPIO_Interrup_flag) for [Anonymous Symbol]
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_L_Last) for LQ_encoder_L_Last
    lq_encoder.o(.text.GROUP1_IRQHandler) refers to lq_encoder.o(.bss.LQ_encoder_R_Last) for LQ_encoder_R_Last
    lq_encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to lq_encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    lq_encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to lq_encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    lq_encoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to lq_encoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    lq_encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to lq_encoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_encoder.o(.text.Encoder_Init) for Encoder_Init
    lq_encoder.o(.text.LQ_Test_Encoder) refers to printfa.o(i.__0sprintf) for sprintf
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_encoder.o(.text.LQ_Test_Encoder) refers to include.o(.text.delay_ms) for delay_ms
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_oled.o(.bss.txt) for txt
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_encoder.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_encoder.o(.bss.LQ_encoder_L) for LQ_encoder_L
    lq_encoder.o(.text.LQ_Test_Encoder) refers to lq_encoder.o(.bss.LQ_encoder_R) for LQ_encoder_R
    lq_encoder.o(.ARM.exidx.text.LQ_Test_Encoder) refers to lq_encoder.o(.text.LQ_Test_Encoder) for [Anonymous Symbol]
    lq_motor.o(.text.PWM_Set) refers to fflti.o(.text) for __aeabi_i2f
    lq_motor.o(.text.PWM_Set) refers to fdiv.o(.text) for __aeabi_fdiv
    lq_motor.o(.text.PWM_Set) refers to ffixi.o(.text) for __aeabi_f2iz
    lq_motor.o(.text.PWM_Set) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    lq_motor.o(.ARM.exidx.text.PWM_Set) refers to lq_motor.o(.text.PWM_Set) for [Anonymous Symbol]
    lq_motor.o(.text.Motor_Ctrl) refers to lq_motor.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lq_motor.o(.text.Motor_Ctrl) refers to lq_motor.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lq_motor.o(.text.Motor_Ctrl) refers to lq_motor.o(.text.PWM_Set) for PWM_Set
    lq_motor.o(.ARM.exidx.text.Motor_Ctrl) refers to lq_motor.o(.text.Motor_Ctrl) for [Anonymous Symbol]
    lq_motor.o(.ARM.exidx.text.DL_GPIO_setPins) refers to lq_motor.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    lq_motor.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to lq_motor.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_motor.o(.text.LQ_Test_Motor) refers to printfa.o(i.__0sprintf) for sprintf
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_motor.o(.text.Motor_Ctrl) for Motor_Ctrl
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_motor.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    lq_motor.o(.text.LQ_Test_Motor) refers to include.o(.text.delay_ms) for delay_ms
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_oled.o(.bss.txt) for txt
    lq_motor.o(.text.LQ_Test_Motor) refers to lq_motor.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_motor.o(.ARM.exidx.text.LQ_Test_Motor) refers to lq_motor.o(.text.LQ_Test_Motor) for [Anonymous Symbol]
    lq_motor.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to lq_motor.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    lq_servo.o(.text.Servo_Ctrl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    lq_servo.o(.ARM.exidx.text.Servo_Ctrl) refers to lq_servo.o(.text.Servo_Ctrl) for [Anonymous Symbol]
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_servo.o(.text.LQ_Test_Servo) refers to printfa.o(i.__0sprintf) for sprintf
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_servo.o(.text.Servo_Ctrl) for Servo_Ctrl
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_servo.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_oled.o(.bss.txt) for txt
    lq_servo.o(.text.LQ_Test_Servo) refers to lq_servo.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_servo.o(.ARM.exidx.text.LQ_Test_Servo) refers to lq_servo.o(.text.LQ_Test_Servo) for [Anonymous Symbol]
    lq_servo.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to lq_servo.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    lq_usart.o(.text.fputc) refers to lq_usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    lq_usart.o(.text.fputc) refers to lq_usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    lq_usart.o(.ARM.exidx.text.fputc) refers to lq_usart.o(.text.fputc) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.DL_UART_isBusy) refers to lq_usart.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.DL_UART_transmitData) refers to lq_usart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    lq_usart.o(.text.uart_init) refers to lq_usart.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    lq_usart.o(.text.uart_init) refers to lq_usart.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    lq_usart.o(.ARM.exidx.text.uart_init) refers to lq_usart.o(.text.uart_init) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to lq_usart.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to lq_usart.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    lq_usart.o(.text.uart0_send_char) refers to lq_usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    lq_usart.o(.text.uart0_send_char) refers to lq_usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    lq_usart.o(.ARM.exidx.text.uart0_send_char) refers to lq_usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    lq_usart.o(.text.uart0_send_string) refers to lq_usart.o(.text.uart0_send_char) for uart0_send_char
    lq_usart.o(.ARM.exidx.text.uart0_send_string) refers to lq_usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    lq_usart.o(.text.UART0_IRQHandler) refers to bsp_uart.o(.text.Uart_getdata) for Uart_getdata
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.bss.uart_data) for uart_data
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.bss.rx0Index) for rx0Index
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.bss.tx0Index) for tx0Index
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.bss.uart0_Buffer) for uart0_Buffer
    lq_usart.o(.text.UART0_IRQHandler) refers to lq_usart.o(.bss.uart0_flag) for uart0_flag
    lq_usart.o(.ARM.exidx.text.UART0_IRQHandler) refers to lq_usart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to lq_usart.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    lq_usart.o(.ARM.exidx.text.DL_UART_receiveData) refers to lq_usart.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) refers to lq_lsm6dsr.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) refers to lq_lsm6dsr.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) refers to lq_gpio.o(.text.LQ_GPIO_readPins) for LQ_GPIO_readPins
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_ReadWriteBytes) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) for [Anonymous Symbol]
    lq_lsm6dsr.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to lq_lsm6dsr.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    lq_lsm6dsr.o(.ARM.exidx.text.DL_GPIO_setPins) refers to lq_lsm6dsr.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) for LQ_LSM6DSR_SPI_ReadWriteBytes
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_ReadData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_WriteData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes) for LQ_LSM6DSR_SPI_ReadWriteBytes
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_WriteData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_WriteData) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_GetDeviceID) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData) for LQ_LSM6DSR_SPI_ReadData
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_GetDeviceID) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_GetDeviceID) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_WriteRegister) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_WriteData) for LQ_LSM6DSR_SPI_WriteData
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_WriteRegister) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_WriteRegister) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadRegister) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData) for LQ_LSM6DSR_SPI_ReadData
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_ReadRegister) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadRegister) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadMultipleRegisters) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData) for LQ_LSM6DSR_SPI_ReadData
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_ReadMultipleRegisters) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadMultipleRegisters) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_WriteRegister) for LQ_LSM6DSR_WriteRegister
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_GetDeviceID) for LQ_LSM6DSR_GetDeviceID
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) refers to include.o(.text.delay_ms) for delay_ms
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_CheckDeviceID) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_WriteRegister) for LQ_LSM6DSR_WriteRegister
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init) refers to include.o(.text.delay_ms) for delay_ms
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) for LQ_LSM6DSR_CheckDeviceID
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_Init) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_LSM6DSR_Read6AxisData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadMultipleRegisters) for LQ_LSM6DSR_ReadMultipleRegisters
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_Read6AxisData) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_Read6AxisData) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init) for LQ_LSM6DSR_Init
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID) for LQ_LSM6DSR_CheckDeviceID
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to printfa.o(i.__0sprintf) for sprintf
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.text.LQ_LSM6DSR_Read6AxisData) for LQ_LSM6DSR_Read6AxisData
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_oled.o(.bss.txt) for txt
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_Z) for LQ_LSM6DSR_Gyro_Z
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_Y) for LQ_LSM6DSR_Gyro_Y
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_X) for LQ_LSM6DSR_Acc_X
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_Y) for LQ_LSM6DSR_Acc_Y
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_Z) for LQ_LSM6DSR_Acc_Z
    lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_X) for LQ_LSM6DSR_Gyro_X
    lq_lsm6dsr.o(.ARM.exidx.text.LQ_Test_LSM6DSR) refers to lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR) for [Anonymous Symbol]
    lq_tracking.o(.text.Tracking_Adc_Init) refers to lq_tracking.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    lq_tracking.o(.ARM.exidx.text.Tracking_Adc_Init) refers to lq_tracking.o(.text.Tracking_Adc_Init) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to lq_tracking.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    lq_tracking.o(.text.Tracking_IO_Set) refers to lq_tracking.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lq_tracking.o(.text.Tracking_IO_Set) refers to lq_tracking.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lq_tracking.o(.ARM.exidx.text.Tracking_IO_Set) refers to lq_tracking.o(.text.Tracking_IO_Set) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to lq_tracking.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.DL_GPIO_setPins) refers to lq_tracking.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    lq_tracking.o(.text.Tracking_Adc_once) refers to lq_tracking.o(.text.DL_ADC12_startConversion) for DL_ADC12_startConversion
    lq_tracking.o(.text.Tracking_Adc_once) refers to lq_tracking.o(.text.DL_ADC12_getMemResult) for DL_ADC12_getMemResult
    lq_tracking.o(.text.Tracking_Adc_once) refers to lq_tracking.o(.bss.Tracking_Adc_Check) for Tracking_Adc_Check
    lq_tracking.o(.ARM.exidx.text.Tracking_Adc_once) refers to lq_tracking.o(.text.Tracking_Adc_once) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.DL_ADC12_startConversion) refers to lq_tracking.o(.text.DL_ADC12_startConversion) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.DL_ADC12_getMemResult) refers to lq_tracking.o(.text.DL_ADC12_getMemResult) for [Anonymous Symbol]
    lq_tracking.o(.text.ADC0_IRQHandler) refers to lq_tracking.o(.text.DL_ADC12_getPendingInterrupt) for DL_ADC12_getPendingInterrupt
    lq_tracking.o(.text.ADC0_IRQHandler) refers to lq_tracking.o(.bss.Tracking_Adc_Check) for Tracking_Adc_Check
    lq_tracking.o(.ARM.exidx.text.ADC0_IRQHandler) refers to lq_tracking.o(.text.ADC0_IRQHandler) for [Anonymous Symbol]
    lq_tracking.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt) refers to lq_tracking.o(.text.DL_ADC12_getPendingInterrupt) for [Anonymous Symbol]
    lq_tracking.o(.text.Tracking_Value_once) refers to lq_tracking.o(.text.Tracking_IO_Set) for Tracking_IO_Set
    lq_tracking.o(.text.Tracking_Value_once) refers to lq_tracking.o(.text.Tracking_Adc_once) for Tracking_Adc_once
    lq_tracking.o(.text.Tracking_Value_once) refers to dfltui.o(.text) for __aeabi_ui2d
    lq_tracking.o(.text.Tracking_Value_once) refers to dmul.o(.text) for __aeabi_dmul
    lq_tracking.o(.text.Tracking_Value_once) refers to dfixui.o(.text) for __aeabi_d2uiz
    lq_tracking.o(.text.Tracking_Value_once) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    lq_tracking.o(.ARM.exidx.text.Tracking_Value_once) refers to lq_tracking.o(.text.Tracking_Value_once) for [Anonymous Symbol]
    lq_tracking.o(.text.Tracking_Value_Acquire) refers to lq_tracking.o(.text.Tracking_Value_once) for Tracking_Value_once
    lq_tracking.o(.text.Tracking_Value_Acquire) refers to lq_tracking.o(.bss.LQ_Tracking_Value) for LQ_Tracking_Value
    lq_tracking.o(.ARM.exidx.text.Tracking_Value_Acquire) refers to lq_tracking.o(.text.Tracking_Value_Acquire) for [Anonymous Symbol]
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_tracking.o(.text.Tracking_Adc_Init) for Tracking_Adc_Init
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_oled.o(.text.OLED_Init) for OLED_Init
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_tracking.o(.text.Tracking_Value_Acquire) for Tracking_Value_Acquire
    lq_tracking.o(.text.LQ_Test_Tracking) refers to printfa.o(i.__0sprintf) for sprintf
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_oled.o(.text.OLED_ShowString) for OLED_ShowString
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_oled.o(.text.OLED_Refresh) for OLED_Refresh
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_oled.o(.bss.txt) for txt
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_tracking.o(.rodata.str1.1) for [Anonymous Symbol]
    lq_tracking.o(.text.LQ_Test_Tracking) refers to lq_tracking.o(.bss.LQ_Tracking_Value) for LQ_Tracking_Value
    lq_tracking.o(.ARM.exidx.text.LQ_Test_Tracking) refers to lq_tracking.o(.text.LQ_Test_Tracking) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to lq_usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to lq_usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to lq_usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to lq_usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sscanf.o(.text) refers (Special) to _scanf_int.o(.text) for _scanf_int
    sscanf.o(.text) refers (Special) to _scanf_longlong.o(.text) for _scanf_longlong
    sscanf.o(.text) refers (Special) to scanf_fp.o(.text) for _scanf_real
    sscanf.o(.text) refers (Special) to _scanf_str.o(.text) for _scanf_string
    sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    _scanf_longlong.o(.text) refers to llmul.o(.text) for __aeabi_lmul
    _scanf_longlong.o(.text) refers to _chval.o(.text) for _chval
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to llmul.o(.text) for __aeabi_lmul
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_longlong.o(.text) for _scanf_longlong
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    dfltul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing bsp_uart.o(.text), (0 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.bsp_uart_init), (8 bytes).
    Removing bsp_uart.o(.text.Test_uart), (204 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.Test_uart), (8 bytes).
    Removing bsp_uart.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bsp_uart.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.Uart_getdata), (8 bytes).
    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.PlacePID_Control), (8 bytes).
    Removing empty.o(.ARM.exidx.text.PID_Realize), (8 bytes).
    Removing empty.o(.ARM.exidx.text.range_protect), (8 bytes).
    Removing empty.o(.ARM.exidx.text.Adc_Normalize), (8 bytes).
    Removing empty.o(.ARM.exidx.text.AD_Get), (8 bytes).
    Removing empty.o(.text.Angle_calculate), (2 bytes).
    Removing empty.o(.ARM.exidx.text.Angle_calculate), (8 bytes).
    Removing empty.o(.ARM.exidx.text.Dir_Control), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing empty.o(.ARM.exidx.text.Motor_Control), (8 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG6_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing empty.o(.bss.Angle_gyro_z), (4 bytes).
    Removing empty.o(.bss.gryz_last), (4 bytes).
    Removing empty.o(.data.Angle_ele), (8 bytes).
    Removing empty.o(.data.UP_LOW_Servo_ele), (8 bytes).
    Removing empty.o(.data.LEFT_RIGHT_Servo_ele), (8 bytes).
    Removing empty.o(.data.out_flag), (1 bytes).
    Removing empty.o(.bss.tttttttt), (4 bytes).
    Removing empty.o(.bss.Angle_PID), (56 bytes).
    Removing empty.o(.bss.UP_LOW_Servo_PID), (56 bytes).
    Removing empty.o(.bss.LEFT_RIGHT_Servo_PID), (56 bytes).
    Removing empty.o(.bss.encoder_L_R), (4 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing include.o(.text), (0 bytes).
    Removing include.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing include.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Motor_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Servo_A0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Servo_G0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_Tracking_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (104 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (112 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (20480 bytes).
    Removing lq_gpio.o(.text), (0 bytes).
    Removing lq_gpio.o(.ARM.exidx.text.LQ_GPIO_readPins), (8 bytes).
    Removing lq_gpio.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing lq_gpio.o(.text.LQ_Test_LED), (28 bytes).
    Removing lq_gpio.o(.ARM.exidx.text.LQ_Test_LED), (8 bytes).
    Removing lq_gpio.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing lq_gpio.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing lq_key.o(.text), (0 bytes).
    Removing lq_key.o(.ARM.exidx.text.Key_Control), (8 bytes).
    Removing lq_key.o(.text.Switch_Control), (76 bytes).
    Removing lq_key.o(.ARM.exidx.text.Switch_Control), (8 bytes).
    Removing lq_oled.o(.text), (0 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing lq_oled.o(.text.OLED_ColorTurn), (52 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ColorTurn), (8 bytes).
    Removing lq_oled.o(.text.OLED_DisplayTurn), (72 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DisplayTurn), (8 bytes).
    Removing lq_oled.o(.text.OLED_DisPlay_On), (34 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DisPlay_On), (8 bytes).
    Removing lq_oled.o(.text.OLED_DisPlay_Off), (34 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DisPlay_Off), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_Refresh), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing lq_oled.o(.text.OLED_DrawLine), (290 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DrawLine), (8 bytes).
    Removing lq_oled.o(.text.OLED_DrawCircle), (276 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_DrawCircle), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing lq_oled.o(.text.OLED_Pow), (48 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing lq_oled.o(.text.OLED_ShowNum), (226 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing lq_oled.o(.text.OLED_ShowChinese), (244 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing lq_oled.o(.text.OLED_ScrollDisplay), (328 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ScrollDisplay), (8 bytes).
    Removing lq_oled.o(.text.OLED_ShowPicture), (314 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_ShowPicture), (8 bytes).
    Removing lq_oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing lq_oled.o(.text.LQ_Test_OLED), (228 bytes).
    Removing lq_oled.o(.ARM.exidx.text.LQ_Test_OLED), (8 bytes).
    Removing lq_oled.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing lq_oled.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing lq_oled.o(.data.oled_data1), (4 bytes).
    Removing lq_oled.o(.data.oled_data2), (4 bytes).
    Removing lq_oled.o(.data.oled_data3), (4 bytes).
    Removing lq_oled.o(.rodata.str1.1), (43 bytes).
    Removing lq_oledfont.o(.text), (0 bytes).
    Removing lq_oledfont.o(.rodata.Hzk1), (112 bytes).
    Removing lq_encoder.o(.text), (0 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.Encoder_Init), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing lq_encoder.o(.text.LQ_Test_Encoder), (128 bytes).
    Removing lq_encoder.o(.ARM.exidx.text.LQ_Test_Encoder), (8 bytes).
    Removing lq_encoder.o(.rodata.str1.1), (49 bytes).
    Removing lq_motor.o(.text), (0 bytes).
    Removing lq_motor.o(.ARM.exidx.text.PWM_Set), (8 bytes).
    Removing lq_motor.o(.ARM.exidx.text.Motor_Ctrl), (8 bytes).
    Removing lq_motor.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing lq_motor.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing lq_motor.o(.text.LQ_Test_Motor), (144 bytes).
    Removing lq_motor.o(.ARM.exidx.text.LQ_Test_Motor), (8 bytes).
    Removing lq_motor.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing lq_motor.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing lq_motor.o(.rodata.str1.1), (36 bytes).
    Removing lq_servo.o(.text), (0 bytes).
    Removing lq_servo.o(.ARM.exidx.text.Servo_Ctrl), (8 bytes).
    Removing lq_servo.o(.text.LQ_Test_Servo), (228 bytes).
    Removing lq_servo.o(.ARM.exidx.text.LQ_Test_Servo), (8 bytes).
    Removing lq_servo.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing lq_servo.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing lq_servo.o(.rodata.str1.1), (60 bytes).
    Removing lq_usart.o(.text), (0 bytes).
    Removing lq_usart.o(.text.fputc), (44 bytes).
    Removing lq_usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing lq_usart.o(.text.DL_UART_isBusy), (24 bytes).
    Removing lq_usart.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing lq_usart.o(.text.DL_UART_transmitData), (22 bytes).
    Removing lq_usart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.uart_init), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing lq_usart.o(.text.uart0_send_char), (44 bytes).
    Removing lq_usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing lq_usart.o(.text.uart0_send_string), (60 bytes).
    Removing lq_usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing lq_usart.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing lq_lsm6dsr.o(.text), (0 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadWriteBytes), (188 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_ReadWriteBytes), (8 bytes).
    Removing lq_lsm6dsr.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing lq_lsm6dsr.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_ReadData), (106 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_ReadData), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_SPI_WriteData), (46 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_SPI_WriteData), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_GetDeviceID), (34 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_GetDeviceID), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_WriteRegister), (28 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_WriteRegister), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadRegister), (30 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_ReadRegister), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_ReadMultipleRegisters), (30 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_ReadMultipleRegisters), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_CheckDeviceID), (130 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_CheckDeviceID), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_Init), (90 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_Init), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_LSM6DSR_Read6AxisData), (114 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_LSM6DSR_Read6AxisData), (8 bytes).
    Removing lq_lsm6dsr.o(.text.LQ_Test_LSM6DSR), (380 bytes).
    Removing lq_lsm6dsr.o(.ARM.exidx.text.LQ_Test_LSM6DSR), (8 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_X), (2 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_Y), (2 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Acc_Z), (2 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_X), (2 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_Y), (2 bytes).
    Removing lq_lsm6dsr.o(.bss.LQ_LSM6DSR_Gyro_Z), (2 bytes).
    Removing lq_lsm6dsr.o(.rodata.str1.1), (84 bytes).
    Removing lq_tracking.o(.text), (0 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.Tracking_Adc_Init), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.Tracking_IO_Set), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.Tracking_Adc_once), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.DL_ADC12_startConversion), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.DL_ADC12_getMemResult), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.ADC0_IRQHandler), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.Tracking_Value_once), (8 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.Tracking_Value_Acquire), (8 bytes).
    Removing lq_tracking.o(.text.LQ_Test_Tracking), (184 bytes).
    Removing lq_tracking.o(.ARM.exidx.text.LQ_Test_Tracking), (8 bytes).
    Removing lq_tracking.o(.rodata.str1.1), (97 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

393 unused section(s) (total 32106 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_longlong.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpgt.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    LQ_encoder.c                             0x00000000   Number         0  lq_encoder.o ABSOLUTE
    LQ_gpio.c                                0x00000000   Number         0  lq_gpio.o ABSOLUTE
    LQ_key.c                                 0x00000000   Number         0  lq_key.o ABSOLUTE
    LQ_lsm6dsr.c                             0x00000000   Number         0  lq_lsm6dsr.o ABSOLUTE
    LQ_motor.c                               0x00000000   Number         0  lq_motor.o ABSOLUTE
    LQ_oled.c                                0x00000000   Number         0  lq_oled.o ABSOLUTE
    LQ_oledfont.c                            0x00000000   Number         0  lq_oledfont.o ABSOLUTE
    LQ_servo.c                               0x00000000   Number         0  lq_servo.o ABSOLUTE
    LQ_tracking.c                            0x00000000   Number         0  lq_tracking.o ABSOLUTE
    LQ_usart.c                               0x00000000   Number         0  lq_usart.o ABSOLUTE
    bsp_uart.c                               0x00000000   Number         0  bsp_uart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    include.c                                0x00000000   Number         0  include.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  strstr.o(.text)
    .text                                    0x00000134   Section        0  sscanf.o(.text)
    .text                                    0x00000170   Section        0  fadd.o(.text)
    .text                                    0x00000222   Section        0  fmul.o(.text)
    .text                                    0x0000029c   Section        0  fdiv.o(.text)
    .text                                    0x00000318   Section        0  dadd.o(.text)
    .text                                    0x0000047c   Section        0  dmul.o(.text)
    .text                                    0x0000054c   Section        0  ddiv.o(.text)
    .text                                    0x0000063c   Section        0  fcmplt.o(.text)
    .text                                    0x00000658   Section        0  dcmplt.o(.text)
    .text                                    0x00000684   Section        0  dcmpge.o(.text)
    .text                                    0x000006b0   Section        0  dcmpgt.o(.text)
    .text                                    0x000006dc   Section        0  fflti.o(.text)
    .text                                    0x000006f4   Section        0  dflti.o(.text)
    .text                                    0x0000071c   Section        0  dfltui.o(.text)
    .text                                    0x00000738   Section        0  ffixi.o(.text)
    .text                                    0x0000076c   Section        0  dfixui.o(.text)
    .text                                    0x000007a8   Section        0  f2d.o(.text)
    .text                                    0x000007d0   Section        0  d2f.o(.text)
    .text                                    0x00000808   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000846   Section        0  uldiv.o(.text)
    .text                                    0x000008a6   Section        0  llshl.o(.text)
    .text                                    0x000008c6   Section        0  llushr.o(.text)
    .text                                    0x000008e8   Section        0  llsshr.o(.text)
    .text                                    0x0000090e   Section        0  _scanf_longlong.o(.text)
    .text                                    0x00000a58   Section        0  _scanf_int.o(.text)
    .text                                    0x00000ba2   Section        0  _scanf_str.o(.text)
    _fp_value                                0x00000c8d   Thumb Code   286  scanf_fp.o(.text)
    .text                                    0x00000c8c   Section        0  scanf_fp.o(.text)
    _scanf_char_input                        0x00001001   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x00001000   Section        0  scanf_char.o(.text)
    .text                                    0x0000102c   Section        0  _sgetc.o(.text)
    .text                                    0x00001070   Section        0  iusefp.o(.text)
    .text                                    0x00001070   Section        0  fepilogue.o(.text)
    .text                                    0x000010f2   Section        0  depilogue.o(.text)
    .text                                    0x000011b0   Section        0  dfixul.o(.text)
    .text                                    0x000011f0   Section       40  cdrcmple.o(.text)
    .text                                    0x00001218   Section       48  init.o(.text)
    .text                                    0x00001248   Section        0  llmul.o(.text)
    .text                                    0x00001278   Section        0  isspace_c.o(.text)
    .text                                    0x00001286   Section        0  _chval.o(.text)
    .text                                    0x000012a4   Section        0  _scanf.o(.text)
    .text                                    0x000015dc   Section        0  dfltul.o(.text)
    .text                                    0x000015f8   Section        0  ctype_c.o(.text)
    [Anonymous Symbol]                       0x0000161c   Section        0  lq_tracking.o(.text.ADC0_IRQHandler)
    __arm_cp.8_0                             0x00001638   Number         4  lq_tracking.o(.text.ADC0_IRQHandler)
    [Anonymous Symbol]                       0x0000163c   Section        0  empty.o(.text.AD_Get)
    __arm_cp.4_0                             0x00001690   Number         4  empty.o(.text.AD_Get)
    __arm_cp.4_1                             0x00001694   Number         4  empty.o(.text.AD_Get)
    __arm_cp.4_2                             0x00001698   Number         4  empty.o(.text.AD_Get)
    __arm_cp.4_3                             0x0000169c   Number         4  empty.o(.text.AD_Get)
    [Anonymous Symbol]                       0x000016a0   Section        0  empty.o(.text.Adc_Normalize)
    __arm_cp.3_0                             0x00001738   Number         4  empty.o(.text.Adc_Normalize)
    __arm_cp.3_1                             0x0000173c   Number         4  empty.o(.text.Adc_Normalize)
    DL_ADC12_clearInterruptStatus            0x00001741   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    [Anonymous Symbol]                       0x00001740   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    DL_ADC12_configConversionMem             0x00001759   Thumb Code    74  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    [Anonymous Symbol]                       0x00001758   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    DL_ADC12_enableConversions               0x000017a3   Thumb Code    22  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    [Anonymous Symbol]                       0x000017a2   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    DL_ADC12_enableInterrupt                 0x000017b9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    [Anonymous Symbol]                       0x000017b8   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    DL_ADC12_enablePower                     0x000017d1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    [Anonymous Symbol]                       0x000017d0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    DL_ADC12_getMemResult                    0x000017e5   Thumb Code    44  lq_tracking.o(.text.DL_ADC12_getMemResult)
    [Anonymous Symbol]                       0x000017e4   Section        0  lq_tracking.o(.text.DL_ADC12_getMemResult)
    __arm_cp.7_0                             0x00001810   Number         4  lq_tracking.o(.text.DL_ADC12_getMemResult)
    DL_ADC12_getPendingInterrupt             0x00001815   Thumb Code    18  lq_tracking.o(.text.DL_ADC12_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001814   Section        0  lq_tracking.o(.text.DL_ADC12_getPendingInterrupt)
    DL_ADC12_initSingleSample                0x00001829   Thumb Code    60  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    [Anonymous Symbol]                       0x00001828   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.46_1                            0x00001864   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.46_2                            0x00001868   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.46_3                            0x0000186c   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    DL_ADC12_reset                           0x00001871   Thumb Code    16  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x00001870   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x00001880   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x000018b8   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x000018bc   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_ADC12_setSampleTime0                  0x000018c1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    [Anonymous Symbol]                       0x000018c0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    __arm_cp.48_0                            0x000018d4   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    DL_ADC12_startConversion                 0x000018d9   Thumb Code    24  lq_tracking.o(.text.DL_ADC12_startConversion)
    [Anonymous Symbol]                       0x000018d8   Section        0  lq_tracking.o(.text.DL_ADC12_startConversion)
    __arm_cp.6_0                             0x000018f0   Number         4  lq_tracking.o(.text.DL_ADC12_startConversion)
    [Anonymous Symbol]                       0x000018f4   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x000018ff   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x000018fe   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_GPIO_clearInterruptStatus             0x00001929   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00001928   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00001941   Thumb Code    24  lq_encoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00001940   Section        0  lq_encoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.6_0                             0x00001958   Number         4  lq_encoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x0000195d   Thumb Code    20  empty.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x0000195c   Section        0  empty.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001971   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001970   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001985   Thumb Code    20  lq_oled.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001984   Section        0  lq_oled.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001999   Thumb Code    20  lq_motor.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001998   Section        0  lq_motor.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000019ad   Thumb Code    20  lq_tracking.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000019ac   Section        0  lq_tracking.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableInterrupt                  0x000019c1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x000019c0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.31_0                            0x000019d8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x000019dd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x000019dc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.23_0                            0x000019f0   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x000019f5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x000019f4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    __arm_cp.17_0                            0x00001a08   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00001a0d   Thumb Code    20  lq_encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00001a0c   Section        0  lq_encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.4_0                             0x00001a20   Number         4  lq_encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00001a25   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00001a24   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.26_0                            0x00001a50   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutputFeatures        0x00001a55   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    [Anonymous Symbol]                       0x00001a54   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    DL_GPIO_initPeripheralAnalogFunction     0x00001a81   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00001a80   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00001a95   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00001a94   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    __arm_cp.24_0                            0x00001aac   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralOutputFunction     0x00001ab1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00001ab0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.22_0                            0x00001ac8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00001acd   Thumb Code    22  lq_gpio.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00001acc   Section        0  lq_gpio.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00001ae3   Thumb Code    22  lq_encoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00001ae2   Section        0  lq_encoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00001af9   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00001af8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_0                            0x00001b08   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_1                            0x00001b0c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00001b11   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00001b10   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.29_0                            0x00001b28   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00001b2d   Thumb Code    20  empty.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b2c   Section        0  empty.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00001b41   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b40   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00001b55   Thumb Code    20  lq_oled.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b54   Section        0  lq_oled.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00001b69   Thumb Code    20  lq_motor.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b68   Section        0  lq_motor.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00001b7d   Thumb Code    20  lq_tracking.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b7c   Section        0  lq_tracking.o(.text.DL_GPIO_setPins)
    __arm_cp.4_0                             0x00001b90   Number         4  lq_tracking.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001b94   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00001c48   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00001c4c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001c50   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00001c55   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00001c54   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00001c61   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00001c60   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.36_0                            0x00001c70   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_setBORThreshold                0x00001c75   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00001c74   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.32_0                            0x00001c88   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00001c8d   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00001c8c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00001ca8   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00001cf4   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setSYSOSCFreq                  0x00001cf9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00001cf8   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.34_0                            0x00001d10   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x00001d15   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00001d14   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00001d2c   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00001d4c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00001d50   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x00001d55   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x00001d54   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x00001d61   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001d60   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.52_0                            0x00001d7c   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.52_1                            0x00001d80   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.52_2                            0x00001d84   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    DL_Timer_enableClock                     0x00001d89   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00001d88   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.39_0                            0x00001d98   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00001d9d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00001d9c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00001db5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00001db4   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    DL_Timer_getPendingInterrupt             0x00001dc9   Thumb Code    18  empty.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001dc8   Section        0  empty.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001ddc   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00001ed0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00001ed4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00001ed8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00001edc   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00001ee0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x00001ee4   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00001fc4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00001fc8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00001fcc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x00001fd1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x00001fd0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x00001fe1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001fe0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001ff4   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x0000200c   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00002010   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00002024   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00002028   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00002034   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00002038   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00002050   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x00002055   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x00002054   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.38_0                            0x00002088   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.38_1                            0x0000208c   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x00002091   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00002090   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableInterrupt                  0x000020a9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x000020a8   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.44_0                            0x000020c0   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x000020c5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x000020c4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.19_0                            0x000020d8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x000020dd   Thumb Code    18  lq_usart.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x000020dc   Section        0  lq_usart.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x000020f0   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00002130   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00002134   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_receiveData                      0x00002139   Thumb Code    16  lq_usart.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x00002138   Section        0  lq_usart.o(.text.DL_UART_receiveData)
    __arm_cp.10_0                            0x00002148   Number         4  lq_usart.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x0000214d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x0000214c   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_0                            0x0000215c   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_1                            0x00002160   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00002165   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00002164   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.43_0                            0x000021a0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.43_1                            0x000021a4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.43_2                            0x000021a8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.43_3                            0x000021ac   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000021b0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000021c3   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000021c2   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000021e0   Section        0  empty.o(.text.Dir_Control)
    __arm_cp.6_0                             0x000025ac   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_1                             0x000025b0   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_2                             0x000025b4   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_3                             0x000025b8   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_4                             0x000025bc   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_5                             0x000025c0   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_6                             0x000025c4   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_7                             0x000025c8   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_8                             0x000025cc   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_9                             0x000025d0   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_10                            0x000025d4   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_11                            0x000025d8   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_12                            0x000025dc   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_13                            0x000025e0   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_14                            0x000025e4   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_15                            0x000025e8   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_17                            0x000025ec   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_18                            0x000025f0   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_19                            0x000025f4   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_20                            0x000025f8   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_21                            0x000025fc   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_22                            0x00002600   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_23                            0x00002604   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_24                            0x00002608   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_25                            0x0000260c   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_26                            0x00002610   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_27                            0x00002614   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_28                            0x00002618   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_29                            0x0000261c   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_30                            0x00002620   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_31                            0x00002624   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_32                            0x00002628   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_33                            0x0000262c   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_34                            0x00002630   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_35                            0x00002634   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_38                            0x00002638   Number         4  empty.o(.text.Dir_Control)
    __arm_cp.6_39                            0x0000263c   Number         4  empty.o(.text.Dir_Control)
    [Anonymous Symbol]                       0x00002640   Section        0  lq_encoder.o(.text.Encoder_Init)
    [Anonymous Symbol]                       0x0000265c   Section        0  lq_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_0                             0x000026d8   Number         4  lq_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_1                             0x000026dc   Number         4  lq_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_2                             0x000026e0   Number         4  lq_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_4                             0x000026e4   Number         4  lq_encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x000026e8   Section        0  lq_key.o(.text.Key_Control)
    __arm_cp.0_1                             0x00002804   Number         4  lq_key.o(.text.Key_Control)
    __arm_cp.0_2                             0x00002808   Number         4  lq_key.o(.text.Key_Control)
    __arm_cp.0_3                             0x0000280c   Number         4  lq_key.o(.text.Key_Control)
    __arm_cp.0_4                             0x00002810   Number         4  lq_key.o(.text.Key_Control)
    __arm_cp.0_5                             0x00002814   Number         4  lq_key.o(.text.Key_Control)
    __arm_cp.0_6                             0x00002818   Number         4  lq_key.o(.text.Key_Control)
    [Anonymous Symbol]                       0x0000281c   Section        0  lq_gpio.o(.text.LQ_GPIO_readPins)
    [Anonymous Symbol]                       0x00002858   Section        0  empty.o(.text.Motor_Control)
    __arm_cp.9_0                             0x0000290c   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_1                             0x00002910   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_2                             0x00002914   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_3                             0x00002918   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_4                             0x0000291c   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_5                             0x00002920   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_6                             0x00002924   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_7                             0x00002928   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_8                             0x0000292c   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_9                             0x00002930   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_10                            0x00002934   Number         4  empty.o(.text.Motor_Control)
    __arm_cp.9_11                            0x00002938   Number         4  empty.o(.text.Motor_Control)
    [Anonymous Symbol]                       0x0000293c   Section        0  lq_motor.o(.text.Motor_Ctrl)
    __arm_cp.1_0                             0x000029a4   Number         4  lq_motor.o(.text.Motor_Ctrl)
    __arm_cp.1_1                             0x000029a8   Number         4  lq_motor.o(.text.Motor_Ctrl)
    [Anonymous Symbol]                       0x000029ac   Section        0  lq_oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x00002a08   Section        0  lq_oled.o(.text.OLED_DrawPoint)
    [Anonymous Symbol]                       0x00002a94   Section        0  lq_oled.o(.text.OLED_Init)
    __arm_cp.19_0                            0x00002ba8   Number         4  lq_oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00002bac   Section        0  lq_oled.o(.text.OLED_Refresh)
    __arm_cp.7_0                             0x00002c24   Number         4  lq_oled.o(.text.OLED_Refresh)
    [Anonymous Symbol]                       0x00002c28   Section        0  lq_oled.o(.text.OLED_ShowChar)
    __arm_cp.12_0                            0x00002da0   Number         4  lq_oled.o(.text.OLED_ShowChar)
    __arm_cp.12_1                            0x00002da4   Number         4  lq_oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00002da8   Section        0  lq_oled.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x00002e74   Section        0  lq_oled.o(.text.OLED_WR_Byte)
    __arm_cp.0_0                             0x00002f24   Number         4  lq_oled.o(.text.OLED_WR_Byte)
    __arm_cp.0_1                             0x00002f28   Number         4  lq_oled.o(.text.OLED_WR_Byte)
    [Anonymous Symbol]                       0x00002f2c   Section        0  empty.o(.text.PID_Realize)
    [Anonymous Symbol]                       0x00003034   Section        0  lq_motor.o(.text.PWM_Set)
    __arm_cp.0_0                             0x00003094   Number         4  lq_motor.o(.text.PWM_Set)
    [Anonymous Symbol]                       0x00003098   Section        0  empty.o(.text.PlacePID_Control)
    [Anonymous Symbol]                       0x00003138   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init)
    __arm_cp.9_0                             0x0000319c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init)
    __arm_cp.9_1                             0x000031a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init)
    [Anonymous Symbol]                       0x000031a4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00003408   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x0000340c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00003410   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00003414   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00003418   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init)
    __arm_cp.4_0                             0x00003498   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init)
    __arm_cp.4_2                             0x0000349c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init)
    [Anonymous Symbol]                       0x000034a0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init)
    __arm_cp.5_0                             0x0000354c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init)
    __arm_cp.5_2                             0x00003550   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init)
    [Anonymous Symbol]                       0x00003554   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init)
    __arm_cp.6_0                             0x000035d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init)
    __arm_cp.6_2                             0x000035d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init)
    [Anonymous Symbol]                       0x000035dc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00003624   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00003628   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00003638   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_0                             0x00003660   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_2                             0x00003664   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00003668   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_0                             0x000036a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x000036a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x000036ac   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x000036ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x000036f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x000036f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x000036f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00003778   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x0000377c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00003780   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003784   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003788   Section        0  lq_servo.o(.text.Servo_Ctrl)
    __arm_cp.0_0                             0x00003834   Number         4  lq_servo.o(.text.Servo_Ctrl)
    __arm_cp.0_1                             0x00003838   Number         4  lq_servo.o(.text.Servo_Ctrl)
    __arm_cp.0_2                             0x0000383c   Number         4  lq_servo.o(.text.Servo_Ctrl)
    [Anonymous Symbol]                       0x00003840   Section        0  empty.o(.text.TIMG6_IRQHandler)
    __arm_cp.11_0                            0x00003874   Number         4  empty.o(.text.TIMG6_IRQHandler)
    __arm_cp.11_1                            0x00003878   Number         4  empty.o(.text.TIMG6_IRQHandler)
    __arm_cp.11_2                            0x0000387c   Number         4  empty.o(.text.TIMG6_IRQHandler)
    __arm_cp.11_3                            0x00003880   Number         4  empty.o(.text.TIMG6_IRQHandler)
    __arm_cp.11_4                            0x00003884   Number         4  empty.o(.text.TIMG6_IRQHandler)
    [Anonymous Symbol]                       0x00003888   Section        0  lq_tracking.o(.text.Tracking_Adc_Init)
    [Anonymous Symbol]                       0x00003894   Section        0  lq_tracking.o(.text.Tracking_Adc_once)
    __arm_cp.5_0                             0x000038d4   Number         4  lq_tracking.o(.text.Tracking_Adc_once)
    [Anonymous Symbol]                       0x000038d8   Section        0  lq_tracking.o(.text.Tracking_IO_Set)
    __arm_cp.2_0                             0x00003970   Number         4  lq_tracking.o(.text.Tracking_IO_Set)
    [Anonymous Symbol]                       0x00003974   Section        0  lq_tracking.o(.text.Tracking_Value_Acquire)
    __arm_cp.11_0                            0x000039b0   Number         4  lq_tracking.o(.text.Tracking_Value_Acquire)
    [Anonymous Symbol]                       0x000039b4   Section        0  lq_tracking.o(.text.Tracking_Value_once)
    __arm_cp.10_0                            0x00003b14   Number         4  lq_tracking.o(.text.Tracking_Value_once)
    __arm_cp.10_1                            0x00003b18   Number         4  lq_tracking.o(.text.Tracking_Value_once)
    [Anonymous Symbol]                       0x00003b1c   Section        0  lq_usart.o(.text.UART0_IRQHandler)
    __arm_cp.8_0                             0x00003ba0   Number         4  lq_usart.o(.text.UART0_IRQHandler)
    __arm_cp.8_1                             0x00003ba4   Number         4  lq_usart.o(.text.UART0_IRQHandler)
    __arm_cp.8_2                             0x00003ba8   Number         4  lq_usart.o(.text.UART0_IRQHandler)
    __arm_cp.8_3                             0x00003bac   Number         4  lq_usart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00003bb0   Section        0  bsp_uart.o(.text.Uart_getdata)
    __arm_cp.4_0                             0x00003c18   Number         4  bsp_uart.o(.text.Uart_getdata)
    __arm_cp.4_1                             0x00003c1c   Number         4  bsp_uart.o(.text.Uart_getdata)
    __arm_cp.4_2                             0x00003c20   Number         4  bsp_uart.o(.text.Uart_getdata)
    __arm_cp.4_3                             0x00003c24   Number         4  bsp_uart.o(.text.Uart_getdata)
    __NVIC_ClearPendingIRQ                   0x00003c29   Thumb Code    40  lq_encoder.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003c28   Section        0  lq_encoder.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00003c51   Thumb Code    40  lq_usart.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003c50   Section        0  lq_usart.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.4_0                             0x00003c78   Number         4  lq_usart.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x00003c7d   Thumb Code    40  lq_encoder.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003c7c   Section        0  lq_encoder.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00003ca5   Thumb Code    40  lq_usart.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003ca4   Section        0  lq_usart.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00003ccd   Thumb Code    40  lq_tracking.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003ccc   Section        0  lq_tracking.o(.text.__NVIC_EnableIRQ)
    __arm_cp.1_0                             0x00003cf4   Number         4  lq_tracking.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003cf8   Section        0  bsp_uart.o(.text.bsp_uart_init)
    [Anonymous Symbol]                       0x00003d0c   Section        0  include.o(.text.delay_ms)
    __arm_cp.1_0                             0x00003d20   Number         4  include.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00003d24   Section        0  include.o(.text.delay_us)
    __arm_cp.0_0                             0x00003d90   Number         4  include.o(.text.delay_us)
    __arm_cp.0_1                             0x00003d94   Number         4  include.o(.text.delay_us)
    [Anonymous Symbol]                       0x00003d98   Section        0  empty.o(.text.main)
    __arm_cp.10_0                            0x00003e80   Number         4  empty.o(.text.main)
    __arm_cp.10_1                            0x00003e84   Number         4  empty.o(.text.main)
    __arm_cp.10_2                            0x00003e88   Number         4  empty.o(.text.main)
    __arm_cp.10_3                            0x00003e8c   Number         4  empty.o(.text.main)
    __arm_cp.10_4                            0x00003e90   Number         4  empty.o(.text.main)
    __arm_cp.10_5                            0x00003e94   Number         4  empty.o(.text.main)
    __arm_cp.10_6                            0x00003e98   Number         4  empty.o(.text.main)
    __arm_cp.10_7                            0x00003e9c   Number         4  empty.o(.text.main)
    __arm_cp.10_8                            0x00003ea0   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00003ea4   Section        0  empty.o(.text.range_protect)
    [Anonymous Symbol]                       0x00003f10   Section        0  lq_usart.o(.text.uart_init)
    i.__0sprintf                             0x00003f28   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x00003f50   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00003f80   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00003f90   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00003f98   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00003fa9   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00003fa8   Section        0  printfa.o(i._fp_digits)
    i._is_digit                              0x0000411c   Section        0  scanf_fp.o(i._is_digit)
    _printf_core                             0x0000412d   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x0000412c   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00004819   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00004818   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00004839   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00004838   Section        0  printfa.o(i._printf_pre_padding)
    _sputc                                   0x00004865   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x00004864   Section        0  printfa.o(i._sputc)
    .constdata                               0x0000486e   Section       64  ctype_c.o(.constdata)
    gADC_TrackingClockConfig                 0x00004f4c   Data           8  ti_msp_dl_config.o(.rodata.gADC_TrackingClockConfig)
    [Anonymous Symbol]                       0x00004f4c   Section        0  ti_msp_dl_config.o(.rodata.gADC_TrackingClockConfig)
    gPWM_MotorClockConfig                    0x00004f54   Data           3  ti_msp_dl_config.o(.rodata.gPWM_MotorClockConfig)
    [Anonymous Symbol]                       0x00004f54   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MotorClockConfig)
    gPWM_MotorConfig                         0x00004f58   Data           8  ti_msp_dl_config.o(.rodata.gPWM_MotorConfig)
    [Anonymous Symbol]                       0x00004f58   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MotorConfig)
    gPWM_Servo_A0ClockConfig                 0x00004f60   Data           3  ti_msp_dl_config.o(.rodata.gPWM_Servo_A0ClockConfig)
    [Anonymous Symbol]                       0x00004f60   Section        0  ti_msp_dl_config.o(.rodata.gPWM_Servo_A0ClockConfig)
    gPWM_Servo_A0Config                      0x00004f64   Data           8  ti_msp_dl_config.o(.rodata.gPWM_Servo_A0Config)
    [Anonymous Symbol]                       0x00004f64   Section        0  ti_msp_dl_config.o(.rodata.gPWM_Servo_A0Config)
    gPWM_Servo_G0ClockConfig                 0x00004f6c   Data           3  ti_msp_dl_config.o(.rodata.gPWM_Servo_G0ClockConfig)
    [Anonymous Symbol]                       0x00004f6c   Section        0  ti_msp_dl_config.o(.rodata.gPWM_Servo_G0ClockConfig)
    gPWM_Servo_G0Config                      0x00004f70   Data           8  ti_msp_dl_config.o(.rodata.gPWM_Servo_G0Config)
    [Anonymous Symbol]                       0x00004f70   Section        0  ti_msp_dl_config.o(.rodata.gPWM_Servo_G0Config)
    gSYSPLLConfig                            0x00004f78   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00004f78   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x00004fa0   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00004fa0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00004fa4   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00004fa4   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00004fb8   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00004fb8   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00004fba   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00004fba   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00004fc4   Section        0  bsp_uart.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00004ff6   Section        0  empty.o(.rodata.str1.1)
    GROUP1_IRQHandler.GPIO_Interrup_flag     0x20200068   Data           4  lq_encoder.o(.bss.GROUP1_IRQHandler.GPIO_Interrup_flag)
    [Anonymous Symbol]                       0x20200068   Section        0  lq_encoder.o(.bss.GROUP1_IRQHandler.GPIO_Interrup_flag)
    STACK                                    0x20200950   Section    20480  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000000e1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0000010d   Thumb Code    40  strstr.o(.text)
    sscanf                                   0x00000135   Thumb Code    50  sscanf.o(.text)
    __aeabi_fadd                             0x00000171   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x00000213   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x0000021b   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x00000223   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x0000029d   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x00000319   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x00000461   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x0000046d   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x0000047d   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x0000054d   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmplt                           0x0000063d   Thumb Code    28  fcmplt.o(.text)
    __aeabi_dcmplt                           0x00000659   Thumb Code    44  dcmplt.o(.text)
    __aeabi_dcmpge                           0x00000685   Thumb Code    44  dcmpge.o(.text)
    __aeabi_dcmpgt                           0x000006b1   Thumb Code    44  dcmpgt.o(.text)
    __aeabi_i2f                              0x000006dd   Thumb Code    22  fflti.o(.text)
    __aeabi_i2d                              0x000006f5   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x0000071d   Thumb Code    24  dfltui.o(.text)
    __aeabi_f2iz                             0x00000739   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2uiz                            0x0000076d   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x000007a9   Thumb Code    40  f2d.o(.text)
    __aeabi_d2f                              0x000007d1   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x00000809   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x00000809   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x00000847   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x000008a7   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000008a7   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x000008c7   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000008c7   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x000008e9   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000008e9   Thumb Code     0  llsshr.o(.text)
    _scanf_longlong                          0x0000090f   Thumb Code   330  _scanf_longlong.o(.text)
    _scanf_int                               0x00000a59   Thumb Code   330  _scanf_int.o(.text)
    _scanf_string                            0x00000ba3   Thumb Code   232  _scanf_str.o(.text)
    _scanf_real                              0x00000dab   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x00000dab   Thumb Code   584  scanf_fp.o(.text)
    __vfscanf_char                           0x0000100d   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x0000102d   Thumb Code    32  _sgetc.o(.text)
    _sbackspace                              0x0000104d   Thumb Code    36  _sgetc.o(.text)
    __I$use$fp                               0x00001071   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00001071   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00001081   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000010f3   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x0000110d   Thumb Code   164  depilogue.o(.text)
    __aeabi_d2ulz                            0x000011b1   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x000011f1   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00001219   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00001219   Thumb Code     0  init.o(.text)
    __aeabi_lmul                             0x00001249   Thumb Code    48  llmul.o(.text)
    _ll_mul                                  0x00001249   Thumb Code     0  llmul.o(.text)
    isspace                                  0x00001279   Thumb Code    14  isspace_c.o(.text)
    _chval                                   0x00001287   Thumb Code    30  _chval.o(.text)
    __vfscanf                                0x000012a5   Thumb Code   812  _scanf.o(.text)
    __aeabi_ul2d                             0x000015dd   Thumb Code    22  dfltul.o(.text)
    __ctype_lookup                           0x000015f9   Thumb Code    32  ctype_c.o(.text)
    ADC0_IRQHandler                          0x0000161d   Thumb Code    28  lq_tracking.o(.text.ADC0_IRQHandler)
    AD_Get                                   0x0000163d   Thumb Code    84  empty.o(.text.AD_Get)
    Adc_Normalize                            0x000016a1   Thumb Code   152  empty.o(.text.Adc_Normalize)
    DL_ADC12_setClockConfig                  0x00001881   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x000018f5   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_SYSCTL_configSYSPLL                   0x00001b95   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00001ca9   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00001d2d   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00001ddd   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x00001ee5   Thumb Code   236  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001ff5   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00002011   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00002029   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00002039   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x000020f1   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000021b1   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    Dir_Control                              0x000021e1   Thumb Code   972  empty.o(.text.Dir_Control)
    Encoder_Init                             0x00002641   Thumb Code    28  lq_encoder.o(.text.Encoder_Init)
    GROUP1_IRQHandler                        0x0000265d   Thumb Code   124  lq_encoder.o(.text.GROUP1_IRQHandler)
    Key_Control                              0x000026e9   Thumb Code   284  lq_key.o(.text.Key_Control)
    LQ_GPIO_readPins                         0x0000281d   Thumb Code    58  lq_gpio.o(.text.LQ_GPIO_readPins)
    Motor_Control                            0x00002859   Thumb Code   180  empty.o(.text.Motor_Control)
    Motor_Ctrl                               0x0000293d   Thumb Code   104  lq_motor.o(.text.Motor_Ctrl)
    OLED_Clear                               0x000029ad   Thumb Code    92  lq_oled.o(.text.OLED_Clear)
    OLED_DrawPoint                           0x00002a09   Thumb Code   140  lq_oled.o(.text.OLED_DrawPoint)
    OLED_Init                                0x00002a95   Thumb Code   276  lq_oled.o(.text.OLED_Init)
    OLED_Refresh                             0x00002bad   Thumb Code   120  lq_oled.o(.text.OLED_Refresh)
    OLED_ShowChar                            0x00002c29   Thumb Code   376  lq_oled.o(.text.OLED_ShowChar)
    OLED_ShowString                          0x00002da9   Thumb Code   204  lq_oled.o(.text.OLED_ShowString)
    OLED_WR_Byte                             0x00002e75   Thumb Code   176  lq_oled.o(.text.OLED_WR_Byte)
    PID_Realize                              0x00002f2d   Thumb Code   262  empty.o(.text.PID_Realize)
    PWM_Set                                  0x00003035   Thumb Code    96  lq_motor.o(.text.PWM_Set)
    PlacePID_Control                         0x00003099   Thumb Code   160  empty.o(.text.PlacePID_Control)
    SYSCFG_DL_ADC_Tracking_init              0x00003139   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_Tracking_init)
    SYSCFG_DL_GPIO_init                      0x000031a5   Thumb Code   612  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_PWM_Motor_init                 0x00003419   Thumb Code   128  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Motor_init)
    SYSCFG_DL_PWM_Servo_A0_init              0x000034a1   Thumb Code   172  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_A0_init)
    SYSCFG_DL_PWM_Servo_G0_init              0x00003555   Thumb Code   128  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Servo_G0_init)
    SYSCFG_DL_SYSCTL_init                    0x000035dd   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00003629   Thumb Code    14  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00003639   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00003669   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x000036ad   Thumb Code    64  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x000036f9   Thumb Code   128  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Servo_Ctrl                               0x00003789   Thumb Code   172  lq_servo.o(.text.Servo_Ctrl)
    TIMG6_IRQHandler                         0x00003841   Thumb Code    52  empty.o(.text.TIMG6_IRQHandler)
    Tracking_Adc_Init                        0x00003889   Thumb Code    10  lq_tracking.o(.text.Tracking_Adc_Init)
    Tracking_Adc_once                        0x00003895   Thumb Code    64  lq_tracking.o(.text.Tracking_Adc_once)
    Tracking_IO_Set                          0x000038d9   Thumb Code   152  lq_tracking.o(.text.Tracking_IO_Set)
    Tracking_Value_Acquire                   0x00003975   Thumb Code    60  lq_tracking.o(.text.Tracking_Value_Acquire)
    Tracking_Value_once                      0x000039b5   Thumb Code   352  lq_tracking.o(.text.Tracking_Value_once)
    UART0_IRQHandler                         0x00003b1d   Thumb Code   132  lq_usart.o(.text.UART0_IRQHandler)
    Uart_getdata                             0x00003bb1   Thumb Code   104  bsp_uart.o(.text.Uart_getdata)
    bsp_uart_init                            0x00003cf9   Thumb Code    20  bsp_uart.o(.text.bsp_uart_init)
    delay_ms                                 0x00003d0d   Thumb Code    20  include.o(.text.delay_ms)
    delay_us                                 0x00003d25   Thumb Code   108  include.o(.text.delay_us)
    main                                     0x00003d99   Thumb Code   232  empty.o(.text.main)
    range_protect                            0x00003ea5   Thumb Code   108  empty.o(.text.range_protect)
    uart_init                                0x00003f11   Thumb Code    22  lq_usart.o(.text.uart_init)
    __0sprintf                               0x00003f29   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x00003f29   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x00003f29   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x00003f29   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x00003f29   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x00003f51   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00003f81   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00003f91   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00003f99   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    _is_digit                                0x0000411d   Thumb Code    14  scanf_fp.o(i._is_digit)
    __ctype_categories                       0x0000486e   Data          64  ctype_c.o(.constdata)
    asc2_0806                                0x000048ae   Data         552  lq_oledfont.o(.rodata.asc2_0806)
    asc2_1206                                0x00004ad6   Data        1140  lq_oledfont.o(.rodata.asc2_1206)
    Region$$Table$$Base                      0x00005004   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00005024   Number         0  anon$$obj.o(Region$$Table)
    Adc_Max                                  0x20200000   Data           8  empty.o(.data.Adc_Max)
    Adc_Min                                  0x20200008   Data           8  empty.o(.data.Adc_Min)
    LEFT_RIGHT_Servo_Out                     0x20200010   Data           4  empty.o(.data.LEFT_RIGHT_Servo_Out)
    Left_MOTOR                               0x20200014   Data          12  empty.o(.data.Left_MOTOR)
    Right_MOTOR                              0x20200020   Data          12  empty.o(.data.Right_MOTOR)
    Turn_ele                                 0x2020002c   Data           8  empty.o(.data.Turn_ele)
    UP_LOW_Servo_Out                         0x20200034   Data           4  empty.o(.data.UP_LOW_Servo_Out)
    run                                      0x20200038   Data           1  empty.o(.data.run)
    Adc_Value                                0x20200040   Data           8  empty.o(.bss.Adc_Value)
    Adc_error                                0x20200048   Data          32  empty.o(.bss.Adc_error)
    High_Speed                               0x2020006c   Data           4  empty.o(.bss.High_Speed)
    LEFT_MOTOR_Duty                          0x20200070   Data           4  empty.o(.bss.LEFT_MOTOR_Duty)
    LEFT_RIGHT_Value                         0x20200074   Data           4  empty.o(.bss.LEFT_RIGHT_Value)
    LQ_Tracking_Value                        0x20200078   Data           8  lq_tracking.o(.bss.LQ_Tracking_Value)
    LQ_encoder_L                             0x20200080   Data           4  lq_encoder.o(.bss.LQ_encoder_L)
    LQ_encoder_L_Last                        0x20200084   Data           4  lq_encoder.o(.bss.LQ_encoder_L_Last)
    LQ_encoder_R                             0x20200088   Data           4  lq_encoder.o(.bss.LQ_encoder_R)
    LQ_encoder_R_Last                        0x2020008c   Data           4  lq_encoder.o(.bss.LQ_encoder_R_Last)
    LQ_key1_flag                             0x20200090   Data           1  lq_key.o(.bss.LQ_key1_flag)
    LQ_key2_flag                             0x20200091   Data           1  lq_key.o(.bss.LQ_key2_flag)
    LQ_key3_flag                             0x20200092   Data           1  lq_key.o(.bss.LQ_key3_flag)
    L_error                                  0x20200094   Data           4  empty.o(.bss.L_error)
    Left_High_Speed                          0x20200098   Data           4  empty.o(.bss.Left_High_Speed)
    Left_MOTOR_PID                           0x202000a0   Data          56  empty.o(.bss.Left_MOTOR_PID)
    OLED_GRAM                                0x202000d8   Data        1152  lq_oled.o(.bss.OLED_GRAM)
    OMV                                      0x20200558   Data          16  bsp_uart.o(.bss.OMV)
    RIGHT_MOTOR_Duty                         0x20200568   Data           4  empty.o(.bss.RIGHT_MOTOR_Duty)
    R_error                                  0x2020056c   Data           4  empty.o(.bss.R_error)
    Right_High_Speed                         0x20200570   Data           4  empty.o(.bss.Right_High_Speed)
    Right_MOTOR_PID                          0x20200578   Data          56  empty.o(.bss.Right_MOTOR_PID)
    Tracking_Adc_Check                       0x202005b0   Data           1  lq_tracking.o(.bss.Tracking_Adc_Check)
    Turn_PID_ele                             0x202005b8   Data          56  empty.o(.bss.Turn_PID_ele)
    UP_LOW_Value                             0x202005f0   Data           4  empty.o(.bss.UP_LOW_Value)
    eleOut                                   0x202005f4   Data           4  empty.o(.bss.eleOut)
    eleValue                                 0x202005f8   Data           4  empty.o(.bss.eleValue)
    elemid                                   0x202005fc   Data           4  empty.o(.bss.elemid)
    gPWM_MotorBackup                         0x20200600   Data         188  ti_msp_dl_config.o(.bss.gPWM_MotorBackup)
    gPWM_Servo_A0Backup                      0x202006bc   Data         188  ti_msp_dl_config.o(.bss.gPWM_Servo_A0Backup)
    gTIMER_0Backup                           0x20200778   Data         160  ti_msp_dl_config.o(.bss.gTIMER_0Backup)
    jibu                                     0x20200818   Data           4  empty.o(.bss.jibu)
    last_left_right                          0x2020081c   Data           4  empty.o(.bss.last_left_right)
    last_up_low                              0x20200820   Data           4  empty.o(.bss.last_up_low)
    rx0Index                                 0x20200824   Data           1  lq_usart.o(.bss.rx0Index)
    turn_rrr                                 0x20200825   Data           1  empty.o(.bss.turn_rrr)
    tx0Index                                 0x20200826   Data           1  lq_usart.o(.bss.tx0Index)
    txt                                      0x20200827   Data          32  lq_oled.o(.bss.txt)
    uart0_Buffer                             0x20200847   Data         256  lq_usart.o(.bss.uart0_Buffer)
    uart0_flag                               0x20200947   Data           1  lq_usart.o(.bss.uart0_flag)
    uart_data                                0x20200948   Data           1  lq_usart.o(.bss.uart_data)
    __initial_sp                             0x20205950   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00005068, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00005024, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO          240    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          708  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          785    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          788    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          790    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          792    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          793    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          795    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          797    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          786    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO          241    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          717    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x00000028   Code   RO          719    .text               mc_p.l(strstr.o)
    0x00000134   0x00000134   0x0000003c   Code   RO          749    .text               mc_p.l(sscanf.o)
    0x00000170   0x00000170   0x000000b2   Code   RO          751    .text               mf_p.l(fadd.o)
    0x00000222   0x00000222   0x0000007a   Code   RO          753    .text               mf_p.l(fmul.o)
    0x0000029c   0x0000029c   0x0000007c   Code   RO          755    .text               mf_p.l(fdiv.o)
    0x00000318   0x00000318   0x00000164   Code   RO          757    .text               mf_p.l(dadd.o)
    0x0000047c   0x0000047c   0x000000d0   Code   RO          759    .text               mf_p.l(dmul.o)
    0x0000054c   0x0000054c   0x000000f0   Code   RO          761    .text               mf_p.l(ddiv.o)
    0x0000063c   0x0000063c   0x0000001c   Code   RO          763    .text               mf_p.l(fcmplt.o)
    0x00000658   0x00000658   0x0000002c   Code   RO          765    .text               mf_p.l(dcmplt.o)
    0x00000684   0x00000684   0x0000002c   Code   RO          767    .text               mf_p.l(dcmpge.o)
    0x000006b0   0x000006b0   0x0000002c   Code   RO          769    .text               mf_p.l(dcmpgt.o)
    0x000006dc   0x000006dc   0x00000016   Code   RO          771    .text               mf_p.l(fflti.o)
    0x000006f2   0x000006f2   0x00000002   PAD
    0x000006f4   0x000006f4   0x00000028   Code   RO          773    .text               mf_p.l(dflti.o)
    0x0000071c   0x0000071c   0x0000001c   Code   RO          775    .text               mf_p.l(dfltui.o)
    0x00000738   0x00000738   0x00000032   Code   RO          777    .text               mf_p.l(ffixi.o)
    0x0000076a   0x0000076a   0x00000002   PAD
    0x0000076c   0x0000076c   0x0000003c   Code   RO          779    .text               mf_p.l(dfixui.o)
    0x000007a8   0x000007a8   0x00000028   Code   RO          781    .text               mf_p.l(f2d.o)
    0x000007d0   0x000007d0   0x00000038   Code   RO          783    .text               mf_p.l(d2f.o)
    0x00000808   0x00000808   0x0000003e   Code   RO          804    .text               mc_p.l(uidiv_div0.o)
    0x00000846   0x00000846   0x00000060   Code   RO          810    .text               mc_p.l(uldiv.o)
    0x000008a6   0x000008a6   0x00000020   Code   RO          812    .text               mc_p.l(llshl.o)
    0x000008c6   0x000008c6   0x00000022   Code   RO          814    .text               mc_p.l(llushr.o)
    0x000008e8   0x000008e8   0x00000026   Code   RO          816    .text               mc_p.l(llsshr.o)
    0x0000090e   0x0000090e   0x0000014a   Code   RO          818    .text               mc_p.l(_scanf_longlong.o)
    0x00000a58   0x00000a58   0x0000014a   Code   RO          820    .text               mc_p.l(_scanf_int.o)
    0x00000ba2   0x00000ba2   0x000000e8   Code   RO          822    .text               mc_p.l(_scanf_str.o)
    0x00000c8a   0x00000c8a   0x00000002   PAD
    0x00000c8c   0x00000c8c   0x00000374   Code   RO          824    .text               mc_p.l(scanf_fp.o)
    0x00001000   0x00001000   0x0000002c   Code   RO          828    .text               mc_p.l(scanf_char.o)
    0x0000102c   0x0000102c   0x00000044   Code   RO          830    .text               mc_p.l(_sgetc.o)
    0x00001070   0x00001070   0x00000000   Code   RO          832    .text               mc_p.l(iusefp.o)
    0x00001070   0x00001070   0x00000082   Code   RO          833    .text               mf_p.l(fepilogue.o)
    0x000010f2   0x000010f2   0x000000be   Code   RO          835    .text               mf_p.l(depilogue.o)
    0x000011b0   0x000011b0   0x00000040   Code   RO          839    .text               mf_p.l(dfixul.o)
    0x000011f0   0x000011f0   0x00000028   Code   RO          841    .text               mf_p.l(cdrcmple.o)
    0x00001218   0x00001218   0x00000030   Code   RO          843    .text               mc_p.l(init.o)
    0x00001248   0x00001248   0x00000030   Code   RO          845    .text               mc_p.l(llmul.o)
    0x00001278   0x00001278   0x0000000e   Code   RO          847    .text               mc_p.l(isspace_c.o)
    0x00001286   0x00001286   0x0000001e   Code   RO          849    .text               mc_p.l(_chval.o)
    0x000012a4   0x000012a4   0x00000338   Code   RO          851    .text               mc_p.l(_scanf.o)
    0x000015dc   0x000015dc   0x0000001c   Code   RO          853    .text               mf_p.l(dfltul.o)
    0x000015f8   0x000015f8   0x00000024   Code   RO          855    .text               mc_p.l(ctype_c.o)
    0x0000161c   0x0000161c   0x00000020   Code   RO          495    .text.ADC0_IRQHandler  lq_tracking.o
    0x0000163c   0x0000163c   0x00000064   Code   RO           29    .text.AD_Get        empty.o
    0x000016a0   0x000016a0   0x000000a0   Code   RO           27    .text.Adc_Normalize  empty.o
    0x00001740   0x00001740   0x00000018   Code   RO          205    .text.DL_ADC12_clearInterruptStatus  ti_msp_dl_config.o
    0x00001758   0x00001758   0x0000004a   Code   RO          201    .text.DL_ADC12_configConversionMem  ti_msp_dl_config.o
    0x000017a2   0x000017a2   0x00000016   Code   RO          209    .text.DL_ADC12_enableConversions  ti_msp_dl_config.o
    0x000017b8   0x000017b8   0x00000018   Code   RO          207    .text.DL_ADC12_enableInterrupt  ti_msp_dl_config.o
    0x000017d0   0x000017d0   0x00000014   Code   RO          147    .text.DL_ADC12_enablePower  ti_msp_dl_config.o
    0x000017e4   0x000017e4   0x00000030   Code   RO          493    .text.DL_ADC12_getMemResult  lq_tracking.o
    0x00001814   0x00001814   0x00000012   Code   RO          497    .text.DL_ADC12_getPendingInterrupt  lq_tracking.o
    0x00001826   0x00001826   0x00000002   PAD
    0x00001828   0x00001828   0x00000048   Code   RO          199    .text.DL_ADC12_initSingleSample  ti_msp_dl_config.o
    0x00001870   0x00001870   0x00000010   Code   RO          139    .text.DL_ADC12_reset  ti_msp_dl_config.o
    0x00001880   0x00001880   0x00000040   Code   RO          515    .text.DL_ADC12_setClockConfig  driverlib.a(dl_adc12.o)
    0x000018c0   0x000018c0   0x00000018   Code   RO          203    .text.DL_ADC12_setSampleTime0  ti_msp_dl_config.o
    0x000018d8   0x000018d8   0x0000001c   Code   RO          491    .text.DL_ADC12_startConversion  lq_tracking.o
    0x000018f4   0x000018f4   0x0000000a   Code   RO          527    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x000018fe   0x000018fe   0x00000028   Code   RO          215    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00001926   0x00001926   0x00000002   PAD
    0x00001928   0x00001928   0x00000018   Code   RO          167    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00001940   0x00001940   0x0000001c   Code   RO          354    .text.DL_GPIO_clearInterruptStatus  lq_encoder.o
    0x0000195c   0x0000195c   0x00000014   Code   RO           37    .text.DL_GPIO_clearPins  empty.o
    0x00001970   0x00001970   0x00000014   Code   RO          161    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00001984   0x00001984   0x00000014   Code   RO          281    .text.DL_GPIO_clearPins  lq_oled.o
    0x00001998   0x00001998   0x00000014   Code   RO          377    .text.DL_GPIO_clearPins  lq_motor.o
    0x000019ac   0x000019ac   0x00000014   Code   RO          485    .text.DL_GPIO_clearPins  lq_tracking.o
    0x000019c0   0x000019c0   0x0000001c   Code   RO          169    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x000019dc   0x000019dc   0x00000018   Code   RO          153    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x000019f4   0x000019f4   0x00000018   Code   RO          141    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00001a0c   0x00001a0c   0x00000018   Code   RO          350    .text.DL_GPIO_getEnabledInterruptStatus  lq_encoder.o
    0x00001a24   0x00001a24   0x00000030   Code   RO          159    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00001a54   0x00001a54   0x0000002c   Code   RO          157    .text.DL_GPIO_initDigitalOutputFeatures  ti_msp_dl_config.o
    0x00001a80   0x00001a80   0x00000014   Code   RO          149    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00001a94   0x00001a94   0x0000001c   Code   RO          155    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00001ab0   0x00001ab0   0x0000001c   Code   RO          151    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00001acc   0x00001acc   0x00000016   Code   RO          250    .text.DL_GPIO_readPins  lq_gpio.o
    0x00001ae2   0x00001ae2   0x00000016   Code   RO          352    .text.DL_GPIO_readPins  lq_encoder.o
    0x00001af8   0x00001af8   0x00000018   Code   RO          133    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00001b10   0x00001b10   0x0000001c   Code   RO          165    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00001b2c   0x00001b2c   0x00000014   Code   RO           35    .text.DL_GPIO_setPins  empty.o
    0x00001b40   0x00001b40   0x00000014   Code   RO          163    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00001b54   0x00001b54   0x00000014   Code   RO          279    .text.DL_GPIO_setPins  lq_oled.o
    0x00001b68   0x00001b68   0x00000014   Code   RO          375    .text.DL_GPIO_setPins  lq_motor.o
    0x00001b7c   0x00001b7c   0x00000018   Code   RO          487    .text.DL_GPIO_setPins  lq_tracking.o
    0x00001b94   0x00001b94   0x000000c0   Code   RO          677    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001c54   0x00001c54   0x0000000c   Code   RO          177    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00001c60   0x00001c60   0x00000014   Code   RO          179    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00001c74   0x00001c74   0x00000018   Code   RO          171    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00001c8c   0x00001c8c   0x0000001c   Code   RO          173    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x00001ca8   0x00001ca8   0x00000050   Code   RO          691    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001cf8   0x00001cf8   0x0000001c   Code   RO          175    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00001d14   0x00001d14   0x00000018   Code   RO          181    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00001d2c   0x00001d2c   0x00000028   Code   RO          685    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001d54   0x00001d54   0x0000000c   Code   RO          213    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x00001d60   0x00001d60   0x00000028   Code   RO          211    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001d88   0x00001d88   0x00000014   Code   RO          185    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00001d9c   0x00001d9c   0x00000018   Code   RO          189    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00001db4   0x00001db4   0x00000014   Code   RO          143    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00001dc8   0x00001dc8   0x00000012   Code   RO           45    .text.DL_Timer_getPendingInterrupt  empty.o
    0x00001dda   0x00001dda   0x00000002   PAD
    0x00001ddc   0x00001ddc   0x00000108   Code   RO          616    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00001ee4   0x00001ee4   0x000000ec   Code   RO          540    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x00001fd0   0x00001fd0   0x00000010   Code   RO          135    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x00001fe0   0x00001fe0   0x00000014   Code   RO          187    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00001ff4   0x00001ff4   0x0000001c   Code   RO          582    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00002010   0x00002010   0x00000018   Code   RO          590    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00002028   0x00002028   0x00000010   Code   RO          542    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00002038   0x00002038   0x0000001c   Code   RO          536    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00002054   0x00002054   0x0000003c   Code   RO          183    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x00002090   0x00002090   0x00000016   Code   RO          197    .text.DL_UART_enable  ti_msp_dl_config.o
    0x000020a6   0x000020a6   0x00000002   PAD
    0x000020a8   0x000020a8   0x0000001c   Code   RO          195    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x000020c4   0x000020c4   0x00000018   Code   RO          145    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x000020dc   0x000020dc   0x00000012   Code   RO          423    .text.DL_UART_getPendingInterrupt  lq_usart.o
    0x000020ee   0x000020ee   0x00000002   PAD
    0x000020f0   0x000020f0   0x00000048   Code   RO          637    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00002138   0x00002138   0x00000014   Code   RO          425    .text.DL_UART_receiveData  lq_usart.o
    0x0000214c   0x0000214c   0x00000018   Code   RO          137    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00002164   0x00002164   0x0000004c   Code   RO          193    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000021b0   0x000021b0   0x00000012   Code   RO          639    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000021c2   0x000021c2   0x0000001e   Code   RO          191    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x000021e0   0x000021e0   0x00000460   Code   RO           33    .text.Dir_Control   empty.o
    0x00002640   0x00002640   0x0000001c   Code   RO          342    .text.Encoder_Init  lq_encoder.o
    0x0000265c   0x0000265c   0x0000008c   Code   RO          348    .text.GROUP1_IRQHandler  lq_encoder.o
    0x000026e8   0x000026e8   0x00000134   Code   RO          263    .text.Key_Control   lq_key.o
    0x0000281c   0x0000281c   0x0000003a   Code   RO          248    .text.LQ_GPIO_readPins  lq_gpio.o
    0x00002856   0x00002856   0x00000002   PAD
    0x00002858   0x00002858   0x000000e4   Code   RO           39    .text.Motor_Control  empty.o
    0x0000293c   0x0000293c   0x00000070   Code   RO          373    .text.Motor_Ctrl    lq_motor.o
    0x000029ac   0x000029ac   0x0000005c   Code   RO          293    .text.OLED_Clear    lq_oled.o
    0x00002a08   0x00002a08   0x0000008c   Code   RO          295    .text.OLED_DrawPoint  lq_oled.o
    0x00002a94   0x00002a94   0x00000118   Code   RO          315    .text.OLED_Init     lq_oled.o
    0x00002bac   0x00002bac   0x0000007c   Code   RO          291    .text.OLED_Refresh  lq_oled.o
    0x00002c28   0x00002c28   0x00000180   Code   RO          301    .text.OLED_ShowChar  lq_oled.o
    0x00002da8   0x00002da8   0x000000cc   Code   RO          303    .text.OLED_ShowString  lq_oled.o
    0x00002e74   0x00002e74   0x000000b8   Code   RO          277    .text.OLED_WR_Byte  lq_oled.o
    0x00002f2c   0x00002f2c   0x00000106   Code   RO           23    .text.PID_Realize   empty.o
    0x00003032   0x00003032   0x00000002   PAD
    0x00003034   0x00003034   0x00000064   Code   RO          371    .text.PWM_Set       lq_motor.o
    0x00003098   0x00003098   0x000000a0   Code   RO           21    .text.PlacePID_Control  empty.o
    0x00003138   0x00003138   0x0000006c   Code   RO          125    .text.SYSCFG_DL_ADC_Tracking_init  ti_msp_dl_config.o
    0x000031a4   0x000031a4   0x00000274   Code   RO          111    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00003418   0x00003418   0x00000088   Code   RO          115    .text.SYSCFG_DL_PWM_Motor_init  ti_msp_dl_config.o
    0x000034a0   0x000034a0   0x000000b4   Code   RO          117    .text.SYSCFG_DL_PWM_Servo_A0_init  ti_msp_dl_config.o
    0x00003554   0x00003554   0x00000088   Code   RO          119    .text.SYSCFG_DL_PWM_Servo_G0_init  ti_msp_dl_config.o
    0x000035dc   0x000035dc   0x0000004c   Code   RO          113    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00003628   0x00003628   0x0000000e   Code   RO          127    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00003636   0x00003636   0x00000002   PAD
    0x00003638   0x00003638   0x00000030   Code   RO          121    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00003668   0x00003668   0x00000044   Code   RO          123    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x000036ac   0x000036ac   0x0000004c   Code   RO          107    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x000036f8   0x000036f8   0x00000090   Code   RO          109    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003788   0x00003788   0x000000b8   Code   RO          391    .text.Servo_Ctrl    lq_servo.o
    0x00003840   0x00003840   0x00000048   Code   RO           43    .text.TIMG6_IRQHandler  empty.o
    0x00003888   0x00003888   0x0000000a   Code   RO          479    .text.Tracking_Adc_Init  lq_tracking.o
    0x00003892   0x00003892   0x00000002   PAD
    0x00003894   0x00003894   0x00000044   Code   RO          489    .text.Tracking_Adc_once  lq_tracking.o
    0x000038d8   0x000038d8   0x0000009c   Code   RO          483    .text.Tracking_IO_Set  lq_tracking.o
    0x00003974   0x00003974   0x00000040   Code   RO          501    .text.Tracking_Value_Acquire  lq_tracking.o
    0x000039b4   0x000039b4   0x00000168   Code   RO          499    .text.Tracking_Value_once  lq_tracking.o
    0x00003b1c   0x00003b1c   0x00000094   Code   RO          421    .text.UART0_IRQHandler  lq_usart.o
    0x00003bb0   0x00003bb0   0x00000078   Code   RO           10    .text.Uart_getdata  bsp_uart.o
    0x00003c28   0x00003c28   0x00000028   Code   RO          346    .text.__NVIC_ClearPendingIRQ  lq_encoder.o
    0x00003c50   0x00003c50   0x0000002c   Code   RO          413    .text.__NVIC_ClearPendingIRQ  lq_usart.o
    0x00003c7c   0x00003c7c   0x00000028   Code   RO          344    .text.__NVIC_EnableIRQ  lq_encoder.o
    0x00003ca4   0x00003ca4   0x00000028   Code   RO          415    .text.__NVIC_EnableIRQ  lq_usart.o
    0x00003ccc   0x00003ccc   0x0000002c   Code   RO          481    .text.__NVIC_EnableIRQ  lq_tracking.o
    0x00003cf8   0x00003cf8   0x00000014   Code   RO            2    .text.bsp_uart_init  bsp_uart.o
    0x00003d0c   0x00003d0c   0x00000018   Code   RO           98    .text.delay_ms      include.o
    0x00003d24   0x00003d24   0x00000074   Code   RO           96    .text.delay_us      include.o
    0x00003d98   0x00003d98   0x0000010c   Code   RO           41    .text.main          empty.o
    0x00003ea4   0x00003ea4   0x0000006c   Code   RO           25    .text.range_protect  empty.o
    0x00003f10   0x00003f10   0x00000016   Code   RO          411    .text.uart_init     lq_usart.o
    0x00003f26   0x00003f26   0x00000002   PAD
    0x00003f28   0x00003f28   0x00000028   Code   RO          724    i.__0sprintf        mc_p.l(printfa.o)
    0x00003f50   0x00003f50   0x0000002e   Code   RO          837    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00003f7e   0x00003f7e   0x00000002   PAD
    0x00003f80   0x00003f80   0x0000000e   Code   RO          860    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00003f8e   0x00003f8e   0x00000002   PAD
    0x00003f90   0x00003f90   0x00000002   Code   RO          861    i.__scatterload_null  mc_p.l(handlers.o)
    0x00003f92   0x00003f92   0x00000006   PAD
    0x00003f98   0x00003f98   0x0000000e   Code   RO          862    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00003fa6   0x00003fa6   0x00000002   PAD
    0x00003fa8   0x00003fa8   0x00000174   Code   RO          729    i._fp_digits        mc_p.l(printfa.o)
    0x0000411c   0x0000411c   0x0000000e   Code   RO          826    i._is_digit         mc_p.l(scanf_fp.o)
    0x0000412a   0x0000412a   0x00000002   PAD
    0x0000412c   0x0000412c   0x000006ec   Code   RO          730    i._printf_core      mc_p.l(printfa.o)
    0x00004818   0x00004818   0x00000020   Code   RO          731    i._printf_post_padding  mc_p.l(printfa.o)
    0x00004838   0x00004838   0x0000002c   Code   RO          732    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00004864   0x00004864   0x0000000a   Code   RO          734    i._sputc            mc_p.l(printfa.o)
    0x0000486e   0x0000486e   0x00000040   Data   RO          856    .constdata          mc_p.l(ctype_c.o)
    0x000048ae   0x000048ae   0x00000228   Data   RO          334    .rodata.asc2_0806   lq_oledfont.o
    0x00004ad6   0x00004ad6   0x00000474   Data   RO          335    .rodata.asc2_1206   lq_oledfont.o
    0x00004f4a   0x00004f4a   0x00000002   PAD
    0x00004f4c   0x00004f4c   0x00000008   Data   RO          231    .rodata.gADC_TrackingClockConfig  ti_msp_dl_config.o
    0x00004f54   0x00004f54   0x00000003   Data   RO          221    .rodata.gPWM_MotorClockConfig  ti_msp_dl_config.o
    0x00004f57   0x00004f57   0x00000001   PAD
    0x00004f58   0x00004f58   0x00000008   Data   RO          222    .rodata.gPWM_MotorConfig  ti_msp_dl_config.o
    0x00004f60   0x00004f60   0x00000003   Data   RO          223    .rodata.gPWM_Servo_A0ClockConfig  ti_msp_dl_config.o
    0x00004f63   0x00004f63   0x00000001   PAD
    0x00004f64   0x00004f64   0x00000008   Data   RO          224    .rodata.gPWM_Servo_A0Config  ti_msp_dl_config.o
    0x00004f6c   0x00004f6c   0x00000003   Data   RO          225    .rodata.gPWM_Servo_G0ClockConfig  ti_msp_dl_config.o
    0x00004f6f   0x00004f6f   0x00000001   PAD
    0x00004f70   0x00004f70   0x00000008   Data   RO          226    .rodata.gPWM_Servo_G0Config  ti_msp_dl_config.o
    0x00004f78   0x00004f78   0x00000028   Data   RO          220    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00004fa0   0x00004fa0   0x00000003   Data   RO          227    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00004fa3   0x00004fa3   0x00000001   PAD
    0x00004fa4   0x00004fa4   0x00000014   Data   RO          228    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00004fb8   0x00004fb8   0x00000002   Data   RO          229    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00004fba   0x00004fba   0x0000000a   Data   RO          230    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00004fc4   0x00004fc4   0x00000032   Data   RO           13    .rodata.str1.1      bsp_uart.o
    0x00004ff6   0x00004ff6   0x0000000e   Data   RO           83    .rodata.str1.1      empty.o
    0x00005004   0x00005004   0x00000020   Data   RO          859    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00005028, Size: 0x00005950, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00005028   0x00000008   Data   RW           49    .data.Adc_Max       empty.o
    0x20200008   0x00005030   0x00000008   Data   RW           50    .data.Adc_Min       empty.o
    0x20200010   0x00005038   0x00000004   Data   RW           68    .data.LEFT_RIGHT_Servo_Out  empty.o
    0x20200014   0x0000503c   0x0000000c   Data   RW           69    .data.Left_MOTOR    empty.o
    0x20200020   0x00005048   0x0000000c   Data   RW           70    .data.Right_MOTOR   empty.o
    0x2020002c   0x00005054   0x00000008   Data   RW           61    .data.Turn_ele      empty.o
    0x20200034   0x0000505c   0x00000004   Data   RW           67    .data.UP_LOW_Servo_Out  empty.o
    0x20200038   0x00005060   0x00000001   Data   RW           53    .data.run           empty.o
    0x20200039   0x00005061   0x00000007   PAD
    0x20200040        -       0x00000008   Zero   RW           51    .bss.Adc_Value      empty.o
    0x20200048        -       0x00000020   Zero   RW           57    .bss.Adc_error      empty.o
    0x20200068        -       0x00000004   Zero   RW          362    .bss.GROUP1_IRQHandler.GPIO_Interrup_flag  lq_encoder.o
    0x2020006c        -       0x00000004   Zero   RW           76    .bss.High_Speed     empty.o
    0x20200070        -       0x00000004   Zero   RW           80    .bss.LEFT_MOTOR_Duty  empty.o
    0x20200074        -       0x00000004   Zero   RW           56    .bss.LEFT_RIGHT_Value  empty.o
    0x20200078        -       0x00000008   Zero   RW          505    .bss.LQ_Tracking_Value  lq_tracking.o
    0x20200080        -       0x00000004   Zero   RW          358    .bss.LQ_encoder_L   lq_encoder.o
    0x20200084        -       0x00000004   Zero   RW          360    .bss.LQ_encoder_L_Last  lq_encoder.o
    0x20200088        -       0x00000004   Zero   RW          359    .bss.LQ_encoder_R   lq_encoder.o
    0x2020008c        -       0x00000004   Zero   RW          361    .bss.LQ_encoder_R_Last  lq_encoder.o
    0x20200090        -       0x00000001   Zero   RW          267    .bss.LQ_key1_flag   lq_key.o
    0x20200091        -       0x00000001   Zero   RW          268    .bss.LQ_key2_flag   lq_key.o
    0x20200092        -       0x00000001   Zero   RW          269    .bss.LQ_key3_flag   lq_key.o
    0x20200093   0x00005061   0x00000001   PAD
    0x20200094        -       0x00000004   Zero   RW           58    .bss.L_error        empty.o
    0x20200098        -       0x00000004   Zero   RW           77    .bss.Left_High_Speed  empty.o
    0x2020009c   0x00005061   0x00000004   PAD
    0x202000a0        -       0x00000038   Zero   RW           79    .bss.Left_MOTOR_PID  empty.o
    0x202000d8        -       0x00000480   Zero   RW          321    .bss.OLED_GRAM      lq_oled.o
    0x20200558        -       0x00000010   Zero   RW           12    .bss.OMV            bsp_uart.o
    0x20200568        -       0x00000004   Zero   RW           82    .bss.RIGHT_MOTOR_Duty  empty.o
    0x2020056c        -       0x00000004   Zero   RW           59    .bss.R_error        empty.o
    0x20200570        -       0x00000004   Zero   RW           78    .bss.Right_High_Speed  empty.o
    0x20200574   0x00005061   0x00000004   PAD
    0x20200578        -       0x00000038   Zero   RW           81    .bss.Right_MOTOR_PID  empty.o
    0x202005b0        -       0x00000001   Zero   RW          506    .bss.Tracking_Adc_Check  lq_tracking.o
    0x202005b1   0x00005061   0x00000007   PAD
    0x202005b8        -       0x00000038   Zero   RW           75    .bss.Turn_PID_ele   empty.o
    0x202005f0        -       0x00000004   Zero   RW           55    .bss.UP_LOW_Value   empty.o
    0x202005f4        -       0x00000004   Zero   RW           66    .bss.eleOut         empty.o
    0x202005f8        -       0x00000004   Zero   RW           60    .bss.eleValue       empty.o
    0x202005fc        -       0x00000004   Zero   RW           65    .bss.elemid         empty.o
    0x20200600        -       0x000000bc   Zero   RW          217    .bss.gPWM_MotorBackup  ti_msp_dl_config.o
    0x202006bc        -       0x000000bc   Zero   RW          218    .bss.gPWM_Servo_A0Backup  ti_msp_dl_config.o
    0x20200778        -       0x000000a0   Zero   RW          219    .bss.gTIMER_0Backup  ti_msp_dl_config.o
    0x20200818        -       0x00000004   Zero   RW           72    .bss.jibu           empty.o
    0x2020081c        -       0x00000004   Zero   RW           48    .bss.last_left_right  empty.o
    0x20200820        -       0x00000004   Zero   RW           47    .bss.last_up_low    empty.o
    0x20200824        -       0x00000001   Zero   RW          429    .bss.rx0Index       lq_usart.o
    0x20200825        -       0x00000001   Zero   RW           73    .bss.turn_rrr       empty.o
    0x20200826        -       0x00000001   Zero   RW          430    .bss.tx0Index       lq_usart.o
    0x20200827        -       0x00000020   Zero   RW          325    .bss.txt            lq_oled.o
    0x20200847        -       0x00000100   Zero   RW          431    .bss.uart0_Buffer   lq_usart.o
    0x20200947        -       0x00000001   Zero   RW          428    .bss.uart0_flag     lq_usart.o
    0x20200948        -       0x00000001   Zero   RW          427    .bss.uart_data      lq_usart.o
    0x20200949   0x00005061   0x00000007   PAD
    0x20200950        -       0x00005000   Zero   RW          238    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       140         16         50          0         16       4433   bsp_uart.o
      2536        276         14         57        269      10381   empty.o
       140         12          0          0          0       1153   include.o
       322         24          0          0         20       5977   lq_encoder.o
        80          0          0          0          0       3865   lq_gpio.o
       308         24          0          0          3       3684   lq_key.o
       252         12          0          0          0       6574   lq_motor.o
      1448         24          0          0       1184      10428   lq_oled.o
         0          0       1692          0          0        526   lq_oledfont.o
       184         12          0          0          0       6254   lq_servo.o
       872         40          0          0          9       9822   lq_tracking.o
       292         24          0          0        260       6410   lq_usart.o
        20          4        192          0      20480        648   startup_mspm0g350x_uvision.o
      2842        216        116          0        536      30735   ti_msp_dl_config.o

    ----------------------------------------------------------------------
      9456        <USER>       <GROUP>         60      22804     100890   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          6          3         27          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        64          8          0          0          0       4709   dl_adc12.o
        10          0          0          0          0        790   dl_common.o
       312         24          0          0          0      12865   dl_sysctl_mspm0g1x0x_g3x0x.o
       596        188          0          0          0      41545   dl_timer.o
        90          8          0          0          0      14150   dl_uart.o
        30          0          0          0          0         60   _chval.o
       824         12          0          0          0         84   _scanf.o
       330          0          0          0          0         84   _scanf_int.o
       330          0          0          0          0         84   _scanf_longlong.o
       232          0          0          0          0         84   _scanf_str.o
        68          0          0          0          0         76   _sgetc.o
        36          4         64          0          0         60   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
        14          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        48          0          0          0          0         72   llmul.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2270         94          0          0          0        472   printfa.o
        44          8          0          0          0         84   scanf_char.o
       898         14          0          0          0        184   scanf_fp.o
        60         10          0          0          0         84   sscanf.o
        40          0          0          0          0         72   strstr.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
        44          0          0          0          0         68   dcmpge.o
        44          0          0          0          0         68   dcmpgt.o
        44          0          0          0          0         68   dcmplt.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        60         10          0          0          0         68   dfixui.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
        28          4          0          0          0         68   dfltui.o
        28          6          0          0          0         68   dfltul.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmplt.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        22          0          0          0          0         68   fflti.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      8894        <USER>         <GROUP>          0          0      78007   Library Totals
        20          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1072        228          0          0          0      74059   driverlib.a
      5620        160         64          0          0       2096   mc_p.l
      2182         54          0          0          0       1852   mf_p.l

    ----------------------------------------------------------------------
      8894        <USER>         <GROUP>          0          0      78007   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     18350       1126       2166         60      22804     176653   Grand Totals
     18350       1126       2166         60      22804     176653   ELF Image Totals
     18350       1126       2166         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                20516 (  20.04kB)
    Total RW  Size (RW Data + ZI Data)             22864 (  22.33kB)
    Total ROM Size (Code + RO Data + RW Data)      20576 (  20.09kB)

==============================================================================

