--cpu Cortex-M0+
".\objects\bsp_uart.o"
".\objects\empty.o"
".\objects\include.o"
".\objects\ti_msp_dl_config.o"
".\objects\startup_mspm0g350x_uvision.o"
".\objects\lq_gpio.o"
".\objects\lq_key.o"
".\objects\lq_oled.o"
".\objects\lq_oledfont.o"
".\objects\lq_encoder.o"
".\objects\lq_motor.o"
".\objects\lq_servo.o"
".\objects\lq_usart.o"
".\objects\lq_lsm6dsr.o"
".\objects\lq_tracking.o"
--library_type=microlib --strict --scatter "..\source\ti\devices\msp\m0p\linker_files\keil\mspm0g3507.sct"
../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\LQ_MSPM0GX_LIB.map" -o .\Objects\LQ_MSPM0GX_LIB.axf