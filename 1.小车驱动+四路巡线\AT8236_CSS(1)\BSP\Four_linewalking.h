#ifndef __FOUR_LINEWALKING_H__
#define __FOUR_LINEWALKING_H__	
#include "ti_msp_dl_config.h"
#include "car_control.h"
#include "delay.h"
/*
    简化的直线循迹模块
    从车尾向前看：从左到右巡线传感器的顺序为  L2  L1  R1  R2
    Looking forward from the rear of the vehicle: The order of the line-following sensors from left to right is L2  L1  R1  R2

    传感器引脚映射：
    X1 -> PA24 (L2)
    X2 -> PA25 (L1)
    X3 -> PA26 (R1)
    X4 -> PA27 (R2)
*/

// 传感器读取宏定义 - 修正引脚映射
#define LineWalk_L2_IN		( ( ( DL_GPIO_readPins(sensor_PORT, sensor_X1_PIN) & sensor_X1_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_L1_IN		( ( ( DL_GPIO_readPins(sensor_PORT, sensor_X2_PIN) & sensor_X2_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_R1_IN		( ( ( DL_GPIO_readPins(sensor_PORT, sensor_X3_PIN) & sensor_X3_PIN ) > 0 ) ? 1 : 0 )
#define LineWalk_R2_IN		( ( ( DL_GPIO_readPins(sensor_PORT, sensor_X4_PIN) & sensor_X4_PIN ) > 0 ) ? 1 : 0 )

#define LOW		(0)
#define HIGH	(1)

// 循迹参数
#define LINE_FOLLOW_SPEED   100     // 循迹速度

// 函数声明
void line_follow_init(void);        // 循迹初始化
void simple_line_follow(void);      // 简化的直线循迹
uint8_t detect_black_line(void);    // 检测是否有黑线
void get_sensor_status(uint8_t *l2, uint8_t *l1, uint8_t *r1, uint8_t *r2);  // 获取传感器状态


#endif

