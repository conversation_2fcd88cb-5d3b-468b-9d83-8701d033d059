/*****************************************************************************

  Copyright (C) 2024 Texas Instruments Incorporated - http://www.ti.com/ 

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions 
  are met:

   Redistributions of source code must retain the above copyright 
   notice, this list of conditions and the following disclaimer.

   Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the 
   documentation and/or other materials provided with the   
   distribution.

   Neither the name of Texas Instruments Incorporated nor the names of
   its contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

*****************************************************************************/
; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

;* Note: SRAM on this device is continuous memory but partitioned in the 
;* linker into two separate sections. This is to account for the upper 64kB
;* of SRAM being wiped out upon the device entering any low-power mode 
;* stronger than SLEEP. Thus, it is up to the end-user to enable SRAM_BANK1 
;* for applications where the memory is considered lost outside of RUN 
;* and SLEEP Modes.

LR_IROM1 0x00000000 0x00080000  {    ; load region size_region
  ER_IROM1 0x00000000 ALIGNALL 8 0x00080000 {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }
  RW_IRAM1 0x20200000 0x00010000  { ; SRAM partion 0
   .ANY (+RW +ZI)
   .ANY (.ramfunc)
   .ANY (.TrimTable)
  }
  RW_IRAM2 0x20210000 0x00010000  {  ; SRAM partition 1
   .ANY (.bank1)
  }
}

LR_BCR 0x41C00000 0x00000100  {    ; load region size_region
  BCR_CONFIG 0x41C00000 0x000000FF{
	.ANY (.BCRConfig)
  }
}

LR_BSL 0x41C00100 0x00000100  {    ; load region size_region
   BSL_CONFIG 0x41C00100 0x00000080{
	.ANY (.BSLConfig)
  }
}

LR_DATA 0x41D00000 0x00004000  {    ; load region size_region
   DATA 0x41D00000 0x00004000{
	.ANY (.DataBank)
  }
}