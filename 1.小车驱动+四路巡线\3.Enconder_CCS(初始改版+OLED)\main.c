#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"
#include "bsp_delay.h"
#include "bsp_usart.h"
#include "OLED.h"
#include "stdio.h"

int speed = 0, speed2 = 0;
char buf[50] = {'\0'};
//������
int main(void)
{
    SYSCFG_DL_init();
	
	 //��������жϱ�־
    NVIC_ClearPendingIRQ(MYUART_INST_INT_IRQN);
    //ʹ�ܴ����ж�
    NVIC_EnableIRQ(MYUART_INST_INT_IRQN);
//	
		NVIC_EnableIRQ(GPIO_MULTIPLE_GPIOA_INT_IRQN);//使能外部中断
		init_motor();//启动定时器

		// 初始化OLED显示屏
		OLED_Init();

		// 显示启动信息
		OLED_ShowEncoderInfo();
	
    while (1)
    {
			// 串口输出编码器速度数据
			sprintf(buf,"speed1(10ms):%d\t speed2(10ms):%d\r\n", speed, speed2);
			uart0_send_string(buf);

			// OLED显示编码器速度
			OLED_ShowEncoderSpeed(speed, speed2);

			// 电机控制测试
			L1_control(600, 0);  // L1电机: 速度600, 方向0(反转)
			L2_control(400, 1);  // L2电机: 速度400, 方向1(正转)

			// 延时300ms
			delay_ms(300);
    }
} 



