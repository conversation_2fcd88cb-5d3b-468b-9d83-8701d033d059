<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="0:Eclipse-based"/>
	<ccsVersion value="12.7.1"/>
	<deviceVariant value="Cortex M.MSPM0G3507"/>
	<deviceFamily value="TMS470"/>
	<deviceEndianness value="little"/>
	<codegenToolVersion value="TICLANG_3.2.2.LTS"/>
	<isElfFormat value="true"/>
	<connection value="common/targetdb/connections/segger_j-link_connection.xml"/>
	<rts value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_LP_MSPM0G3507_nortos_ticlang.projectspec.empty_LP_MSPM0G3507_nortos_ticlang,buildProfile=release,isHybrid=true"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="C:/ti/mspm0_sdk_2_05_01_00/2.AT8236_CSS"/>
	<filesToOpen value="empty.syscfg"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
</projectOptions>
