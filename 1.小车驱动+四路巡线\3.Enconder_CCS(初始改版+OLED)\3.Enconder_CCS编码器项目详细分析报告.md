# 3.Enconder_CCS 编码器项目详细分析报告

## 📋 项目概述

### 硬件平台
- **微控制器**: MSPM0G350X (LQFP-64封装)
- **SDK版本**: mspm0_sdk@1.30.00.03
- **开发环境**: Code Composer Studio + TI SysConfig
- **系统时钟**: 32MHz

### 项目功能
这是一个**四路电机驱动 + 双路编码器反馈**的测试项目，主要用于验证编码器速度检测功能。

## 🔧 硬件配置分析 (empty.syscfg)

### 1. 系统时钟配置
```
系统时钟: 32MHz
SYSTICK: 1ms中断周期 (32000计数值)
```

### 2. 引脚分配详细说明

#### **四路电机PWM输出**
```
PWM_L1 (TIMG0): PA12(CCP0), PA13(CCP1)
PWM_R1 (TIMG6): PA21(CCP0), PA22(CCP1)  
PWM_L2 (TIMG7): PA26(CCP0), PA27(CCP1)
PWM_R2 (TIMA0): PA0(CCP0), PA1(CCP1)
```

#### **双路编码器接口**
```
L1编码器:
- A相: PA14 (上升沿中断)
- B相: PA15 (上升沿中断)

L2编码器:
- A相: PA24 (上升沿中断)
- B相: PA25 (上升沿中断)
```

#### **串口通信**
```
UART0: PA10(TX), PA11(RX), 115200bps
```

## 📁 代码模块详细分析

### 1. 主程序模块 (main.c)

#### **全局变量定义**
```c
int speed = 0, speed2 = 0;    // 编码器速度值 (10ms周期内的脉冲数)
char buf[35] = {'\0'};        // 串口发送缓冲区
```

#### **主函数流程**
```c
int main(void)
{
    // 1. 系统初始化
    SYSCFG_DL_init();
    
    // 2. 中断配置
    NVIC_ClearPendingIRQ(MYUART_INST_INT_IRQN);
    NVIC_EnableIRQ(MYUART_INST_INT_IRQN);        // 串口中断
    NVIC_EnableIRQ(GPIO_MULTIPLE_GPIOA_INT_IRQN); // GPIO中断(编码器)
    
    // 3. 电机初始化
    init_motor();
    
    // 4. 主循环
    while (1) 
    {    
        // 格式化速度数据并通过串口发送
        sprintf(buf,"speed1(10ms):%d\t speed2(10ms):%d\r\n",speed,speed2);
        uart0_send_string(buf);
        
        // 电机测试 - 固定速度运行
        L1_control(600,0);  // L1电机: 速度600, 方向0(反转)
        L2_control(400,1);  // L2电机: 速度400, 方向1(正转)
        
        delay_ms(300);      // 延时300ms
    }
}
```

**功能分析**:
- 每300ms通过串口输出一次编码器速度值
- L1和L2电机以固定速度运行，用于测试编码器反馈
- 速度值表示10ms内的编码器脉冲计数

### 2. 编码器模块 (bsp_enconder.c/h)

#### **核心变量**
```c
volatile uint32_t gpioA;                    // GPIO中断状态
volatile int32_t gEncoderCount_L1 = 0;      // L1编码器累计计数
volatile int32_t gEncoderCount_L2 = 0;      // L2编码器累计计数
```

#### **编码器中断处理 (GROUP1_IRQHandler)**
```c
void GROUP1_IRQHandler(void)
{
    // 读取中断信号
    gpioA = DL_GPIO_getEnabledInterruptStatus(GPIOA,
        L1_Enconder_A_pin_14_PIN | L1_Enconder_B_pin_15_PIN|
        L2_Enconder_A_pin_24_PIN | L2_Enconder_B_pin_25_PIN);
 
    // L1编码器A相中断处理
    if((gpioA & L1_Enconder_A_pin_14_PIN) == L1_Enconder_A_pin_14_PIN)
    {
        if(!DL_GPIO_readPins(GPIOA,L1_Enconder_B_pin_15_PIN)) // B相为低电平
        {
            gEncoderCount_L1--;  // 反转
        }
        else // B相为高电平
        {
            gEncoderCount_L1++;  // 正转
        }
    }
    
    // L1编码器B相中断处理
    else if((gpioA & L1_Enconder_B_pin_15_PIN) == L1_Enconder_B_pin_15_PIN)
    {
        if(!DL_GPIO_readPins(GPIOA,L1_Enconder_A_pin_14_PIN)) // A相为低电平
        {
            gEncoderCount_L1++;  // 正转
        }
        else // A相为高电平
        {
            gEncoderCount_L1--;  // 反转
        }
    }
    
    // L2编码器处理逻辑相同...
    
    // 清除中断标志
    DL_GPIO_clearInterruptStatus(GPIOA, L1_Enconder_A_pin_14_PIN|L1_Enconder_B_pin_15_PIN);
    DL_GPIO_clearInterruptStatus(GPIOA, L2_Enconder_A_pin_24_PIN|L2_Enconder_B_pin_25_PIN);
}
```

**编码器工作原理**:
1. **A/B相正交编码**: 通过A相和B相的相位关系判断转向
2. **双边沿检测**: A相和B相都配置为上升沿中断
3. **方向判断**: 中断发生时读取另一相的电平状态判断转向
4. **计数累加**: 根据转向对计数值进行加减操作

### 3. 速度计算模块 (bsp_delay.c)

#### **SYSTICK中断处理 (SysTick_Handler)**
```c
volatile unsigned int getspeed = 10;  // 速度获取计数器 (10ms)

void SysTick_Handler(void)
{
    // 延时功能
    if( delay_times != 0 )
    {
        delay_times--;
    }
    
    // 速度计算 (每10ms执行一次)
    if( getspeed != 0 )
    {
        getspeed--;
    }
    else
    {
        getspeed = 10;                    // 重置为10ms
        speed = gEncoderCount_L1;         // 获取L1编码器10ms内的脉冲数
        speed2 = gEncoderCount_L2;        // 获取L2编码器10ms内的脉冲数
        gEncoderCount_L1 = 0;             // 清零L1计数器
        gEncoderCount_L2 = 0;             // 清零L2计数器
    }
}
```

**速度计算原理**:
1. **SYSTICK配置**: 1ms中断周期
2. **采样周期**: 每10ms采样一次编码器计数
3. **速度值**: speed和speed2表示10ms内的编码器脉冲数
4. **计数清零**: 每次采样后清零累计计数器

### 4. 电机驱动模块 (bsp_at8236.c/h)

#### **电机初始化**
```c
void init_motor(void)
{
    DL_TimerA_startCounter(PWM_L1_INST);  // 启动L1电机PWM
    DL_TimerA_startCounter(PWM_L2_INST);  // 启动L2电机PWM
    DL_TimerA_startCounter(PWM_R1_INST);  // 启动R1电机PWM
    DL_TimerA_startCounter(PWM_R2_INST);  // 启动R2电机PWM
}
```

#### **电机控制函数**
```c
void L1_control(uint16_t motor_speed, uint8_t dir)
{
    if(dir)  // 正转
    {
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_1_INDEX);
    }
    else     // 反转
    {
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_0_INDEX);
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
    }
}
```

**控制方式**:
- **正转**: 通道0输出PWM，通道1输出0
- **反转**: 通道0输出0，通道1输出PWM
- **速度范围**: 0-1000 (对应PWM占空比)

### 5. 串口通信模块 (bsp_usart.c/h)

#### **串口发送函数**
```c
void uart0_send_char(char ch)
{
    while( DL_UART_isBusy(MYUART_INST) == true );  // 等待发送完成
    DL_UART_Main_transmitData(MYUART_INST, ch);    // 发送字符
}

void uart0_send_string(char* str)
{
    while(*str!=0 && str!=0)
    {
        uart0_send_char(*str++);  // 逐字符发送
    }
}
```

#### **串口中断处理**
```c
void MYUART_INST_IRQHandler(void)
{
    switch( DL_UART_getPendingInterrupt(MYUART_INST) )
    {
        case DL_UART_IIDX_RX:  // 接收中断
            uart_data = DL_UART_Main_receiveData(MYUART_INST);
            uart0_send_char(uart_data);  // 回显接收到的数据
            break;
    }
}
```

## 🔍 编码器速度反馈分析

### **如何查看编码器反馈速度**

#### 1. **串口输出方式 (当前实现)**
```
输出格式: "speed1(10ms):123    speed2(10ms):456"
更新频率: 每300ms输出一次
数据含义: 
- speed1: L1编码器在10ms内的脉冲计数
- speed2: L2编码器在10ms内的脉冲计数
```

#### 2. **速度值含义**
```
脉冲数 → 转速换算:
假设编码器分辨率为N脉冲/转
转速(RPM) = (脉冲数 × 6000) / N
例如: 100脉冲/转的编码器，10ms内20个脉冲
转速 = (20 × 6000) / 100 = 1200 RPM
```

#### 3. **实时监测方法**
- **串口助手**: 连接PA10(TX)查看实时数据
- **示波器**: 监测PA14/PA15编码器信号
- **调试器**: 在线查看speed和speed2变量值

### **是否需要单独加OLED屏幕？**

#### **当前状态**: ❌ 无OLED显示
- 项目中没有配置OLED相关的GPIO和SPI接口
- 速度反馈完全依赖串口输出

#### **建议**: ✅ 推荐添加OLED显示

**优势**:
1. **实时显示**: 无需连接电脑即可查看速度
2. **便于调试**: 现场测试时更方便
3. **数据直观**: 可显示更多参数信息
4. **独立运行**: 脱离开发环境使用

**实现方案**:
```c
// 建议添加的OLED显示内容
OLED_ShowString(1, 1, "Encoder Speed:");
OLED_ShowString(2, 1, "L1:");
OLED_ShowNum(2, 4, speed, 4);
OLED_ShowString(2, 9, "pps");
OLED_ShowString(3, 1, "L2:");  
OLED_ShowNum(3, 4, speed2, 4);
OLED_ShowString(3, 9, "pps");
OLED_ShowString(4, 1, "Period: 10ms");
```

## 🎯 项目功能总结

### **实现的功能**
1. ✅ **四路电机PWM控制** - 支持速度和方向控制
2. ✅ **双路编码器检测** - A/B相正交编码器接口
3. ✅ **速度实时计算** - 10ms周期速度采样
4. ✅ **串口数据输出** - 实时速度数据传输
5. ✅ **中断驱动设计** - 高精度时序控制

### **应用场景**
- **电机性能测试**: 验证电机转速特性
- **编码器标定**: 测试编码器精度和响应
- **闭环控制开发**: 为PID控制提供速度反馈
- **系统调试**: 验证硬件连接和软件逻辑

### **扩展建议**
1. **添加OLED显示** - 提高使用便利性
2. **增加PID控制** - 实现闭环速度控制
3. **数据记录功能** - 保存测试数据用于分析
4. **多种速度单位** - 支持RPM、mm/s等单位显示

**结论**: 这是一个功能完整的编码器测试项目，通过串口可以实时查看编码器反馈速度，建议添加OLED显示以提高使用便利性。
