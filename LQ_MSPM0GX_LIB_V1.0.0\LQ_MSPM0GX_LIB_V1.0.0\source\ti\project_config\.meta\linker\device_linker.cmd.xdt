%%{
// For ticlang/default
let Common = system.getScript("/ti/driverlib/Common.js");
let MigrationCommon = system.getScript("/ti/project_config/Common.js");
let Options = system.getScript("/ti/project_config/linker/LINKERMSPM0options.js");

let MigrationController = system.modules["/ti/project_config/ProjectConfig"];
let migration_inst = MigrationController.$static;
let gpn = migration_inst.deviceSpinAdvanced;

let SRAMSection = "SRAM";
if(Common.hasTrimTable()){
    SRAMSection = "SRAM_BANK0";
}

%%}
/*****************************************************************************

  Copyright (C) 2023 Texas Instruments Incorporated - http://www.ti.com/

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

   Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

   Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the
   distribution.

   Neither the name of Texas Instruments Incorporated nor the names of
   its contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

*****************************************************************************/
-uinterruptVectors
--stack_size=`Options.StackSize[gpn]`

% if(Common.hasTrimTable()){
/* Note: SRAM is partitioned into two separate sections SRAM_BANK0 and SRAM_BANK1
 * to account for SRAM_BANK1 being wiped out upon the device entering any low-power
 * mode stronger than SLEEP. Thus, it is up to the end-user to enable SRAM_BANK1 for
 * applications where the memory is considered lost outside of RUN and SLEEP Modes.
 */
% }

MEMORY
{
    FLASH           (RX)  : origin = `Options.FLASHOrigin[gpn]`, length = `Options.FLASHLength[gpn]`
% if(Common.hasTrimTable()){
    SRAM_BANK0      (RWX) : origin = `Options.SRAMOrigin[gpn]`, length = `Options.SRAMLength[gpn]`
    SRAM_BANK1      (RWX) : origin = `Options.SRAMBank1Origin[gpn]`, length = `Options.SRAMBank1Length[gpn]`
% } else{
    SRAM            (RWX) : origin = `Options.SRAMOrigin[gpn]`, length = `Options.SRAMLength[gpn]`
% }
    BCR_CONFIG      (R)   : origin = `Options.BCROrigin[gpn]`, length = `Options.BCRLength[gpn]`
% if(Common.hasBSLConfig()){
    BSL_CONFIG      (R)   : origin = `Options.BSLOrigin[gpn]`, length = `Options.BSLLength[gpn]`
% }
% if(Common.hasDataRegionConfig()){
    DATA            (R)   : origin = `Options.DATAOrigin[gpn]`, length = `Options.DATALength[gpn]`
% }
}

SECTIONS
{
    .intvecs:   > 0x00000000
    .text   : palign(8) {} > FLASH
    .const  : palign(8) {} > FLASH
    .cinit  : palign(8) {} > FLASH
    .pinit  : palign(8) {} > FLASH
    .rodata : palign(8) {} > FLASH
    .ARM.exidx    : palign(8) {} > FLASH
    .init_array   : palign(8) {} > FLASH
    .binit        : palign(8) {} > FLASH
    .TI.ramfunc   : load = FLASH, palign(8), run=`SRAMSection`, table(BINIT)

    .vtable :   > `SRAMSection`
    .args   :   > `SRAMSection`
    .data   :   > `SRAMSection`
    .bss    :   > `SRAMSection`
    .sysmem :   > `SRAMSection`
% /* Trim Workaround exclusive to MSPM0GX51X */
% if(Common.hasTrimTable()){
    .TrimTable :  > `SRAMSection`
% }
    .stack  :   > `SRAMSection` (HIGH)

    .BCRConfig  : {} > BCR_CONFIG
% if(Common.hasBSLConfig()){
    .BSLConfig  : {} > BSL_CONFIG
% }
% if(Common.hasDataRegionConfig()){
    .DataBank   : {} > DATA
% }
}
