# OLED显示编码器速度使用说明

## 📋 功能概述

为3.Enconder_CCS项目添加了OLED显示模块，可以实时显示编码器速度数据，无需连接电脑即可查看编码器反馈信息。

## 🔧 硬件连接

### **OLED模块连接 (SSD1306 I2C接口)**
```
OLED模块    →    MSPM0G3507
VCC         →    3.3V
GND         →    GND  
SCL         →    PA2 (I2C时钟线)
SDA         →    PA3 (I2C数据线)
```

### **编码器连接 (已有配置)**
```
L1编码器:
- A相: PA14 (上升沿中断)
- B相: PA15 (上升沿中断)

L2编码器:
- A相: PA24 (上升沿中断)  
- B相: PA25 (上升沿中断)
```

### **电机连接 (已有配置)**
```
L1电机: PA12(PWM), PA13(PWM)
L2电机: PA26(PWM), PA27(PWM)
R1电机: PA21(PWM), PA22(PWM)
R2电机: PA0(PWM), PA1(PWM)
```

## 📁 新增文件

### **1. OLED.h** - OLED驱动头文件
- 包含所有OLED相关的函数声明
- 定义OLED引脚和参数
- 字体大小枚举定义

### **2. OLED.c** - OLED驱动实现文件
- 软件I2C通信实现
- SSD1306 OLED控制函数
- 字符、字符串、数字显示函数
- 编码器专用显示函数

## 🔧 修改的文件

### **main.c 主要修改**
```c
// 新增头文件包含
#include "OLED.h"

// 主函数中新增初始化
OLED_Init();                    // 初始化OLED
OLED_ShowEncoderInfo();         // 显示启动信息

// 主循环中新增显示
OLED_ShowEncoderSpeed(speed, speed2);  // 实时显示编码器速度
```

## 📺 OLED显示内容

### **启动画面 (2秒)**
```
MSPM0G3507
Encoder Test

L1: PA14/PA15
L2: PA24/PA25

Ready...
```

### **运行时显示 (每300ms更新)**
```
Encoder Speed
Period: 10ms

L1:  123 pps
L2: -456 pps

RPM1:  738
RPM2: -2736
```

### **显示参数说明**
- **pps**: 每10ms的脉冲数 (pulses per 10ms)
- **RPM**: 估算转速 (假设100PPR编码器)
- **正负值**: 表示编码器转向 (正转/反转)

## 🎯 核心功能函数

### **1. OLED初始化**
```c
void OLED_Init(void);
```
- 初始化I2C通信
- 配置SSD1306显示参数
- 清屏并准备显示

### **2. 编码器速度显示**
```c
void OLED_ShowEncoderSpeed(int speed1, int speed2);
```
- 显示两路编码器的实时速度
- 自动计算并显示RPM值
- 支持正负值显示

### **3. 启动信息显示**
```c
void OLED_ShowEncoderInfo(void);
```
- 显示系统信息和引脚配置
- 2秒后自动切换到速度显示

### **4. 基础显示函数**
```c
void OLED_ShowString(uint8_t x, uint8_t y, char *str, OLED_FontSize_t size);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t size);
void OLED_ShowSignedNum(uint8_t x, uint8_t y, int32_t num, uint8_t len, OLED_FontSize_t size);
```

## 🔄 工作流程

### **系统启动流程**
```
1. 系统初始化 (SYSCFG_DL_init)
2. 中断配置 (串口、GPIO)
3. 电机初始化 (init_motor)
4. OLED初始化 (OLED_Init)
5. 显示启动信息 (OLED_ShowEncoderInfo)
6. 进入主循环
```

### **主循环流程 (每300ms)**
```
1. 串口输出速度数据
2. OLED显示速度数据
3. 电机控制测试
4. 延时300ms
```

### **中断处理流程**
```
SYSTICK中断 (1ms):
- 延时计数
- 每10ms采样编码器速度

GPIO中断 (编码器):
- A/B相信号处理
- 方向判断和计数
```

## 📊 数据格式

### **串口输出格式**
```
speed1(10ms):123    speed2(10ms):-456
speed1(10ms):89     speed2(10ms):234
```

### **OLED显示格式**
```
行1: 标题 "Encoder Speed"
行2: 采样周期 "Period: 10ms"
行3: 空行
行4: L1编码器 "L1:  123 pps"
行5: L2编码器 "L2: -456 pps"
行6: 空行
行7: L1转速 "RPM1:  738"
行8: L2转速 "RPM2: -2736"
```

## 🎛️ 参数配置

### **编码器参数**
```c
// 转速计算公式 (假设100PPR编码器)
int rpm1 = (speed1 * 600) / 100;
// RPM = (pps * 60 * 1000ms) / (PPR * 10ms)
```

### **显示更新频率**
```c
delay_ms(300);  // 主循环300ms周期
getspeed = 10;  // SYSTICK中10ms采样周期
```

### **OLED参数**
```c
#define OLED_WIDTH       128    // 宽度128像素
#define OLED_HEIGHT      64     // 高度64像素
#define OLED_I2C_ADDR    0x78   // I2C地址
```

## 🛠️ 使用步骤

### **1. 硬件准备**
- 连接SSD1306 OLED到PA2(SCL)和PA3(SDA)
- 确保编码器正确连接到PA14/PA15和PA24/PA25
- 检查电源连接(3.3V)

### **2. 软件编译**
- 在CCS中打开项目
- 确保新增的OLED.c和OLED.h文件已添加到项目
- 编译并下载到MSPM0G3507

### **3. 运行测试**
- 上电后OLED显示启动信息
- 2秒后切换到速度显示界面
- 电机开始运行，观察编码器速度变化
- 同时可通过串口查看数据

### **4. 数据观察**
- **静止时**: speed1和speed2应为0
- **运行时**: 根据电机转速显示相应数值
- **方向**: 正负值表示不同转向

## 🔧 故障排除

### **OLED无显示**
```
检查项目:
✓ 电源连接 (3.3V/GND)
✓ I2C连接 (PA2/PA3)
✓ OLED模块是否正常
✓ I2C地址是否正确 (0x78)
```

### **编码器数值异常**
```
检查项目:
✓ 编码器连接 (PA14/PA15, PA24/PA25)
✓ 编码器电源
✓ 电机是否转动
✓ 编码器信号质量
```

### **显示数据不更新**
```
检查项目:
✓ 主循环是否正常运行
✓ SYSTICK中断是否正常
✓ delay_ms函数是否正常
```

## 🎉 总结

通过添加OLED显示模块，编码器测试项目现在具备了：

1. ✅ **实时显示** - 无需电脑即可查看编码器速度
2. ✅ **双路监测** - 同时显示两路编码器数据
3. ✅ **转速计算** - 自动换算为RPM值
4. ✅ **方向指示** - 正负值表示转向
5. ✅ **友好界面** - 清晰的显示布局
6. ✅ **独立运行** - 脱离开发环境使用

这大大提高了编码器测试的便利性和直观性，特别适合现场调试和演示使用。
