/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.19.0+3426"}
 */

/**
 * Import the modules used in this configuration.
 */
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const PWM2   = PWM.addInstance();
const PWM3   = PWM.addInstance();
const PWM4   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
PWM1.clockDivider               = 8;
PWM1.$name                      = "PWM_L1";
PWM1.clockPrescale              = 40;
PWM1.peripheral.ccp0Pin.$assign = "PA12";
PWM1.peripheral.ccp1Pin.$assign = "PA13";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

PWM2.clockDivider                       = 8;
PWM2.$name                              = "PWM_R1";
PWM2.clockPrescale                      = 40;
PWM2.peripheral.$assign                 = "TIMG6";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM3.$name                              = "PWM_L2";
PWM3.clockDivider                       = 8;
PWM3.clockPrescale                      = 40;
PWM3.peripheral.ccp0Pin.$assign         = "PA26";
PWM3.peripheral.ccp1Pin.$assign         = "PA27";
PWM3.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC4";
PWM3.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC5";
PWM3.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM3.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM3.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM4.$name               = "PWM_R2";
PWM4.clockDivider        = 8;
PWM4.clockPrescale       = 40;
PWM4.PWM_CHANNEL_0.$name = "ti_driverlib_pwm_PWMTimerCC8";
PWM4.ccp0PinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM4.PWM_CHANNEL_1.$name = "ti_driverlib_pwm_PWMTimerCC9";
PWM4.ccp1PinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric9";

SYSCTL.forceDefaultClkConfig = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
PWM1.peripheral.$suggestSolution           = "TIMG0";
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
PWM2.peripheral.ccp0Pin.$suggestSolution   = "PA21";
PWM2.peripheral.ccp1Pin.$suggestSolution   = "PA22";
PWM3.peripheral.$suggestSolution           = "TIMG7";
PWM4.peripheral.$suggestSolution           = "TIMA0";
PWM4.peripheral.ccp0Pin.$suggestSolution   = "PA0";
PWM4.peripheral.ccp1Pin.$suggestSolution   = "PA1";
SYSCTL.peripheral.$suggestSolution         = "SYSCTL";
