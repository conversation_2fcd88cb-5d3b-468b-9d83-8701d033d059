# AT8236_CSS(1) 代码错误修复说明

## 修复的主要问题

### 1. UART相关错误修复

#### 问题描述
编译时出现以下错误：
- `use of undeclared identifier 'UART_1_INST_INT_IRQN'`
- `call to undeclared library function 'printf'`

#### 原因分析
1. **UART配置不匹配**: usart.c中试图使用UART_1，但empty.syscfg中只配置了UART_0
2. **printf函数未声明**: 缺少stdio.h头文件包含和函数声明

#### 修复方案

**1. 修复usart.c中的UART配置**
```c
// 修复前
void USART_Init(void)
{
    SYSCFG_DL_init();
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);  // 错误：UART_1未配置
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);        // 错误：UART_1未配置
}

// 修复后
void USART_Init(void)
{
    SYSCFG_DL_init();
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);  // 只使用UART_0
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);        // 只使用UART_0
}
```

**2. 修复usart.h头文件**
```c
// 修复前
#ifndef __USART_H__
#define __USART_H__
#include "ti_msp_dl_config.h"
void USART_Init(void);
void USART_SendData(unsigned char data);
#endif

// 修复后
#ifndef __USART_H__
#define __USART_H__
#include "ti_msp_dl_config.h"
#include <stdio.h>                              // 添加stdio.h

void USART_Init(void);
void USART_SendData(unsigned char data);
int fputc(int ch, FILE *stream);                // 添加printf重定向声明
#endif
```

**3. 修复main.c头文件包含**
```c
// 修复前
#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"
#include "car_control.h"
#include "Four_linewalking.h"
#include "delay.h"
#include "usart.h"

// 修复后
#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"
#include "car_control.h"
#include "Four_linewalking.h"
#include "delay.h"
#include "usart.h"
#include <stdio.h>                              // 添加stdio.h支持printf
```

### 2. 系统配置验证

#### UART配置状态
- **配置的UART**: 只有UART_0 (对应硬件UART1)
- **引脚配置**: PA8(TX), PA9(RX)
- **波特率**: 9600

#### PWM配置状态
- **PWM_L1**: TIMG0, 引脚PA12/PA13
- **PWM_R1**: TIMG6, 引脚PA21/PA22
- **时钟配置**: 分频8, 预分频40, 频率100kHz

#### 传感器配置状态
- **X1(L2)**: PA24
- **X2(L1)**: PA25  
- **X3(R1)**: PA26
- **X4(R2)**: PA27

## 修复后的系统架构

### 模块依赖关系
```
main.c
├── usart.h (串口通信)
├── car_control.h (车辆控制)
│   └── bsp_at8236.h (电机驱动)
├── Four_linewalking.h (循迹控制)
│   ├── car_control.h
│   └── delay.h
└── delay.h (延时功能)
```

### 编译顺序
1. 系统配置文件生成 (ti_msp_dl_config.h)
2. 基础模块编译 (delay, usart)
3. 硬件驱动编译 (bsp_at8236)
4. 应用模块编译 (car_control, Four_linewalking)
5. 主程序编译 (main.c)

## 验证步骤

### 1. 编译验证
```bash
# 在CCS中执行Clean & Build
# 确保无编译错误和警告
```

### 2. 功能验证
1. **串口输出测试**: 检查printf输出是否正常
2. **电机控制测试**: 验证L1和R1电机是否正常工作
3. **传感器测试**: 检查四路传感器读取是否正确
4. **循迹功能测试**: 验证直线循迹是否正常

### 3. 系统测试流程
1. **上电初始化**: 系统正常启动
2. **测试运行**: 前进5秒后停止
3. **循迹模式**: 检测黑线并沿线行驶
4. **停止条件**: 无黑线时自动停止

## 注意事项

### 1. 配置文件同步
- 确保empty.syscfg与生成的ti_msp_dl_config.h一致
- 如有配置更改，需重新生成配置文件

### 2. 硬件连接
- 确认UART连接: PA8(TX) → 调试器RX, PA9(RX) → 调试器TX
- 确认电机连接: L1→PA12/PA13, R1→PA21/PA22
- 确认传感器连接: PA24/PA25/PA26/PA27

### 3. 调试建议
- 使用串口输出调试信息
- 分模块测试各功能
- 检查PWM波形是否正确

## 总结

通过以上修复，解决了主要的编译错误：
1. ✅ 修复了UART配置不匹配问题
2. ✅ 解决了printf函数未声明问题
3. ✅ 确保了头文件包含的完整性
4. ✅ 验证了双驱动系统配置的正确性

系统现在应该可以正常编译和运行，实现预期的双驱动循迹功能。
