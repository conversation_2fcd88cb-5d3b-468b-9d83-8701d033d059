%%{
/*
 * Copyright (c) 2022, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== mclkSource.Board.c.xdt ========
 */

    let Common = system.getScript("/ti/driverlib/Common.js");

    let mod = system.modules["/ti/clockTree/mux.js"];

    let mclkMuxInst = _.find(mod.$instances, ['$name', 'SYSCTLMUX']);
    let hsclkMuxInst = _.find(mod.$instances, ['$name', 'HSCLKMUX']);
    let exhfMuxInst = _.find(mod.$instances, ['$name', 'EXHFMUX']);

    mod = system.modules["/ti/clockTree/oscillator.js"];

    let sysoscInst = _.find(mod.$instances, ['$name', 'SYSOSC'])

    if(_.isUndefined(mclkMuxInst) || _.isUndefined(sysoscInst) ||
        (Common.isDeviceM0G() && _.isUndefined(hsclkMuxInst))) {
        throw 'Not all MCLK elements are defined appropriately';
    }

    let mclkStr = ""

    /* Traverse the MCLK table to look for clock source */
    if(_.endsWith(mclkMuxInst.inputSelect, "LFCLK")){
        mclkStr = "DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK(" + sysoscInst.disableSYSOSC.toString() + ");"; /* TODO: allow SYSOSC to be disabled */
    } else {

        if(!_.isUndefined(hsclkMuxInst)) {
            if(_.endsWith(hsclkMuxInst.inputSelect, "SYSOSC")){
                return; /* nothing to print */
            } else if (_.endsWith(hsclkMuxInst.inputSelect, "HFCLK")){
                mclkStr = "DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_HSCLK_SOURCE_HFCLK);";
            } else {
                mclkStr = "DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_HSCLK_SOURCE_SYSPLL);";
            }
        } else if(!_.isUndefined(exhfMuxInst)) {
            if(_.endsWith(exhfMuxInst.inputSelect, "SYSOSC")){
                return; /* nothing to print */
            } else {
                mclkStr = "DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK();";
            }

        } else {
            return; /* only SYSOSC supported, nothing to print */
        }
    }
%%}
%
    `mclkStr`
%
