%%{
/*
 * Copyright (c) 2018-2019, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CO<PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== WWDT.Board.c.xdt ========
 */
    /* args[] passed by /ti/drivers/templates/Board.c.xdt during function call: */
    let WWDT = args[0];
    let content = args[1];
    let Common = system.getScript("/ti/driverlib/Common.js");

    /* shorthand names for some common references in template below */
    let instances = WWDT.$instances;
    if (instances.length == 0) return;

    /*
     * Standard Trampoline:
     * In order to preserve spacing, it is important to also set the boolean
     * values in the templates object based on whether that condition should
     * produce any code
     * Example:
     * templates: {
     *       boardc: "/ti/driverlib/comp/COMP.Board.c.xdt",
     *       boardh: "/ti/driverlib/comp/COMP.Board.h.xdt",
     *       Call: true,
     *       Reset: false,
     *       Power: true,
     *       GPIO: false,
     *       Function: true
     * },
     */

    switch(content) {
        case "Call":
            printCall();
            break;
        case "Reset":
            printReset();
            break;
        case "Power":
            printPower();
            break;
        case "GPIO":
            printGPIO();
            break;
        case "Function":
            printFunction();
            break;
        default:
            /* do nothing */
            break;
    }

%%}
%
% function printCall() {
% for(let inst of instances){
    SYSCFG_DL_`inst.$name`_init();
%   }
% }
%
% function printReset() {
%   for(let inst of instances) {
%   let instName = inst.$name + "_INST";
    DL_WWDT_reset(`instName`);
%   }
% }
%
% function printPower() {
%   for(let inst of instances) {
%   let instName = inst.$name + "_INST";
    DL_WWDT_enablePower(`instName`);
%   }
% }
%
% function printGPIO() {
% /* Not applicable to WWDT */
% }
%
% /* Main Function */
% function printFunction() {
%   for(let inst of instances) {
%   let instName = inst.$name + "_INST";
%   let sleepMode = "STOP"
%   if(inst.sleepMode) {
%       sleepMode = "RUN"
%   }
SYSCONFIG_WEAK void SYSCFG_DL_`inst.$name`_init(void)
{
%   if (inst.wwdtMode == "INTERVAL") {
    /*
     * Initialize `inst.$name` in Interval Timer mode with following settings
     *   Watchdog Source Clock = (LFCLK Freq) / (WWDT Clock Divider)
     *                         = 32768Hz / `inst.clockDivider` = `inst.clockSourceCalculated`
     *   Watchdog Period       = (WWDT Clock Divider) ∗ (WWDT Period Count) / 32768Hz
     *                         = `inst.clockDivider` * 2^`inst.periodCount` / 32768Hz = `inst.periodCalculated`
     */
    DL_WWDT_initIntervalTimerMode(`instName`, DL_WWDT_CLOCK_DIVIDE_`inst.clockDivider`,
        DL_WWDT_TIMER_PERIOD_`inst.periodCount`_BITS, DL_WWDT_`sleepMode`_IN_SLEEP);
%} else {
    /*
     * Initialize `inst.$name` in Watchdog mode with following settings
     *   Watchdog Source Clock = (LFCLK Freq) / (WWDT Clock Divider)
     *                         = 32768Hz / `inst.clockDivider` = `inst.clockSourceCalculated`
     *   Watchdog Period       = (WWDT Clock Divider) ∗ (WWDT Period Count) / 32768Hz
     *                         = `inst.clockDivider` * 2^`inst.periodCount` / 32768Hz = `inst.periodCalculated`
     *   Window0 Closed Period = (WWDT Period) * (Window0 Closed Percent)
     *                         = `inst.periodCalculated` * `inst.window0ClosedPeriod`% = `inst.window0ClosedPeriodCalculated`
     *   Window1 Closed Period = (WWDT Period) * (Window1 Closed Percent)
     *                         = `inst.periodCalculated` * `inst.window1ClosedPeriod`% = `inst.window1ClosedPeriodCalculated`
     */
    DL_WWDT_initWatchdogMode(`instName`, DL_WWDT_CLOCK_DIVIDE_`inst.clockDivider`,
        DL_WWDT_TIMER_PERIOD_`inst.periodCount`_BITS, DL_WWDT_`sleepMode`_IN_SLEEP,
        DL_WWDT_WINDOW_PERIOD_`inst.window0ClosedPeriod`, DL_WWDT_WINDOW_PERIOD_`inst.window1ClosedPeriod`);

    /* Set Window`inst.activeWindow` as active window */
    DL_WWDT_setActiveWindow(`instName`, DL_WWDT_WINDOW`inst.activeWindow`);
%} //if (inst.wwdtMode == "INTERVAL")
%   if (inst.enabledInterrupts) {

    /* Enable WWDT Interval Timer interrupt */
    DL_WWDT_enableInterrupt(`instName`);
%   }

}

%   } // end of for inst in instances
%
% } // printFunction()
