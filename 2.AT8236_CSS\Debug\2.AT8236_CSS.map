******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Wed Jul 24 15:08:05 2024

OUTPUT FILE NAME:   <2.AT8236_CSS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000699


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000798  0001f868  R  X
  SRAM                  20200000   00008000  000003ac  00007c54  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000798   00000798    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000690   00000690    r-x .text
  00000750    00000750    00000030   00000030    r-- .rodata
  00000780    00000780    00000018   00000018    r-- .cinit
20200000    20200000    000001ac   00000000    rw-
  20200000    20200000    000001ac   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000690     
                  000000c0    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00000184    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L1_init)
                  00000204    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L2_init)
                  00000284    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R1_init)
                  00000304    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R2_init)
                  00000384    00000078     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  000003fc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000450    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000004a0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000004e0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000520    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000055c    00000038     main.o (.text.main)
                  00000594    00000034     bsp_at8236.o (.text.L1_control)
                  000005c8    00000034     bsp_at8236.o (.text.L2_control)
                  000005fc    00000034     bsp_at8236.o (.text.R1_control)
                  00000630    00000034     bsp_at8236.o (.text.R2_control)
                  00000664    00000034     bsp_at8236.o (.text.init_motor)
                  00000698    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000006c0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000006dc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000006f8    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000710    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000726    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000728    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000738    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  00000742    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000746    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000074a    00000004            : exit.c.obj (.text:abort)
                  0000074e    00000002     --HOLE-- [fill = 0]

.cinit     0    00000780    00000018     
                  00000780    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000788    00000004     (__TI_handler_table)
                  0000078c    00000008     (__TI_cinit_table)
                  00000794    00000004     --HOLE-- [fill = 0]

.rodata    0    00000750    00000030     
                  00000750    00000008     ti_msp_dl_config.o (.rodata.gPWM_L1Config)
                  00000758    00000008     ti_msp_dl_config.o (.rodata.gPWM_L2Config)
                  00000760    00000008     ti_msp_dl_config.o (.rodata.gPWM_R1Config)
                  00000768    00000008     ti_msp_dl_config.o (.rodata.gPWM_R2Config)
                  00000770    00000003     ti_msp_dl_config.o (.rodata.gPWM_L1ClockConfig)
                  00000773    00000003     ti_msp_dl_config.o (.rodata.gPWM_L2ClockConfig)
                  00000776    00000003     ti_msp_dl_config.o (.rodata.gPWM_R1ClockConfig)
                  00000779    00000003     ti_msp_dl_config.o (.rodata.gPWM_R2ClockConfig)
                  0000077c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001ac     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_R2Backup)
                  202000bc    00000078     (.common:gPWM_L2Backup)
                  20200134    00000078     (.common:gPWM_R1Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             804    44        428    
       startup_mspm0g350x_ticlang.o   6      192       0      
       main.o                         56     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         866    236       428    
                                                              
    .\BSP\
       bsp_at8236.o                   260    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         260    0         0      
                                                              
    D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         130    0         0      
                                                              
    W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     412    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         422    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      20        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1678   256       940    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000078c records: 1, size/record: 8, table size: 8
	.bss: load addr=00000780, load size=00000008 bytes, run addr=20200000, run size=000001ac bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000788 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000727  ADC0_IRQHandler                 
00000727  ADC1_IRQHandler                 
00000727  AES_IRQHandler                  
0000074a  C$$EXIT                         
00000727  CANFD0_IRQHandler               
00000727  DAC0_IRQHandler                 
00000739  DL_Common_delayCycles           
00000385  DL_TimerA_initPWMMode           
000000c1  DL_Timer_initPWMMode            
000006c1  DL_Timer_setCaptCompUpdateMethod
000006f9  DL_Timer_setCaptureCompareOutCtl
00000729  DL_Timer_setCaptureCompareValue 
000006dd  DL_Timer_setClockConfig         
00000727  DMA_IRQHandler                  
00000727  Default_Handler                 
00000727  GROUP0_IRQHandler               
00000727  GROUP1_IRQHandler               
00000727  HardFault_Handler               
00000727  I2C0_IRQHandler                 
00000727  I2C1_IRQHandler                 
00000595  L1_control                      
000005c9  L2_control                      
00000727  NMI_Handler                     
00000727  PendSV_Handler                  
000005fd  R1_control                      
00000631  R2_control                      
00000727  RTC_IRQHandler                  
00000743  Reset_Handler                   
00000727  SPI0_IRQHandler                 
00000727  SPI1_IRQHandler                 
00000727  SVC_Handler                     
00000451  SYSCFG_DL_GPIO_init             
00000185  SYSCFG_DL_PWM_L1_init           
00000205  SYSCFG_DL_PWM_L2_init           
00000285  SYSCFG_DL_PWM_R1_init           
00000305  SYSCFG_DL_PWM_R2_init           
000004a1  SYSCFG_DL_SYSCTL_init           
000004e1  SYSCFG_DL_init                  
000003fd  SYSCFG_DL_initPower             
00000727  SysTick_Handler                 
00000727  TIMA0_IRQHandler                
00000727  TIMA1_IRQHandler                
00000727  TIMG0_IRQHandler                
00000727  TIMG12_IRQHandler               
00000727  TIMG6_IRQHandler                
00000727  TIMG7_IRQHandler                
00000727  TIMG8_IRQHandler                
00000727  UART0_IRQHandler                
00000727  UART1_IRQHandler                
00000727  UART2_IRQHandler                
00000727  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
0000078c  __TI_CINIT_Base                 
00000794  __TI_CINIT_Limit                
00000794  __TI_CINIT_Warm                 
00000788  __TI_Handler_Table_Base         
0000078c  __TI_Handler_Table_Limit        
00000521  __TI_auto_init_nobinit_nopinit  
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000711  __TI_zero_init_nomemset         
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000699  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000747  _system_pre_init                
0000074b  abort                           
ffffffff  binit                           
202000bc  gPWM_L2Backup                   
20200134  gPWM_R1Backup                   
20200000  gPWM_R2Backup                   
00000665  init_motor                      
00000000  interruptVectors                
0000055d  main                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  DL_Timer_initPWMMode            
00000185  SYSCFG_DL_PWM_L1_init           
00000200  __STACK_SIZE                    
00000205  SYSCFG_DL_PWM_L2_init           
00000285  SYSCFG_DL_PWM_R1_init           
00000305  SYSCFG_DL_PWM_R2_init           
00000385  DL_TimerA_initPWMMode           
000003fd  SYSCFG_DL_initPower             
00000451  SYSCFG_DL_GPIO_init             
000004a1  SYSCFG_DL_SYSCTL_init           
000004e1  SYSCFG_DL_init                  
00000521  __TI_auto_init_nobinit_nopinit  
0000055d  main                            
00000595  L1_control                      
000005c9  L2_control                      
000005fd  R1_control                      
00000631  R2_control                      
00000665  init_motor                      
00000699  _c_int00_noargs                 
000006c1  DL_Timer_setCaptCompUpdateMethod
000006dd  DL_Timer_setClockConfig         
000006f9  DL_Timer_setCaptureCompareOutCtl
00000711  __TI_zero_init_nomemset         
00000727  ADC0_IRQHandler                 
00000727  ADC1_IRQHandler                 
00000727  AES_IRQHandler                  
00000727  CANFD0_IRQHandler               
00000727  DAC0_IRQHandler                 
00000727  DMA_IRQHandler                  
00000727  Default_Handler                 
00000727  GROUP0_IRQHandler               
00000727  GROUP1_IRQHandler               
00000727  HardFault_Handler               
00000727  I2C0_IRQHandler                 
00000727  I2C1_IRQHandler                 
00000727  NMI_Handler                     
00000727  PendSV_Handler                  
00000727  RTC_IRQHandler                  
00000727  SPI0_IRQHandler                 
00000727  SPI1_IRQHandler                 
00000727  SVC_Handler                     
00000727  SysTick_Handler                 
00000727  TIMA0_IRQHandler                
00000727  TIMA1_IRQHandler                
00000727  TIMG0_IRQHandler                
00000727  TIMG12_IRQHandler               
00000727  TIMG6_IRQHandler                
00000727  TIMG7_IRQHandler                
00000727  TIMG8_IRQHandler                
00000727  UART0_IRQHandler                
00000727  UART1_IRQHandler                
00000727  UART2_IRQHandler                
00000727  UART3_IRQHandler                
00000729  DL_Timer_setCaptureCompareValue 
00000739  DL_Common_delayCycles           
00000743  Reset_Handler                   
00000747  _system_pre_init                
0000074a  C$$EXIT                         
0000074b  abort                           
00000788  __TI_Handler_Table_Base         
0000078c  __TI_CINIT_Base                 
0000078c  __TI_Handler_Table_Limit        
00000794  __TI_CINIT_Limit                
00000794  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_R2Backup                   
202000bc  gPWM_L2Backup                   
20200134  gPWM_R1Backup                   
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[91 symbols]
