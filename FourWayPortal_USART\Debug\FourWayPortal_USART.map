******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Mar 29 21:56:21 2025

OUTPUT FILE NAME:   <FourWayPortal_USART.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000334d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003830  0001c7d0  R  X
  SRAM                  20200000   00008000  00001069  00006f97  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003830   00003830    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003520   00003520    r-x .text
  000035e0    000035e0    000001c0   000001c0    r-- .rodata
  000037a0    000037a0    00000090   00000090    r-- .cinit
20200000    20200000    00000e69   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    00000404   00000000    rw- .bss
  20200c04    20200c04    00000265   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003520     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000101e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001020    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000115c    00000120            : _printfi.c.obj (.text._pconv_e)
                  0000127c    00000114     Four_linewalking.o (.text.Four_LineWalking)
                  00001390    00000114     libc.a : memory.c.obj (.text.aligned_alloc)
                  000014a4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000015b0    000000f8     libc.a : fputs.c.obj (.text.fputs)
                  000016a8    000000f4     app_motor.o (.text.Set_Motor)
                  0000179c    000000e8     libc.a : memory.c.obj (.text.free)
                  00001884    000000e4     bsp_motor_usart.o (.text.Send_Motor_ArrayU8)
                  00001968    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001a4c    000000e0     libc.a : setvbuf.c.obj (.text.setvbuf)
                  00001b2c    000000d8            : s_scalbn.c.obj (.text.scalbn)
                  00001c04    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001cdc    000000a2                            : udivmoddi4.S.obj (.text)
                  00001d7e    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00001e18    00000090     app_motor.o (.text.Motion_Car_Control)
                  00001ea8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001f34    0000000c                            : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00001f40    00000088     libc.a : strcmp-armv6m.S.obj (.text:strcmp)
                  00001fc8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000204a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000204c    0000007c     libc.a : fclose.c.obj (.text.__TI_closefile)
                  000020c8    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002144    00000074     app_motor_usart.o (.text.Deal_Control_Rxtemp)
                  000021b8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000222c    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000229c    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  0000230c    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  00002378    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  000023e4    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  00002450    0000006c            : getdevice.c.obj (.text.getdevice)
                  000024bc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002524    00000068     app_motor_usart.o (.text.send_motor_PID)
                  0000258c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000025f2    00000064            : _io_perm.c.obj (.text.__TI_wrt_ok)
                  00002656    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002658    00000064     libc.a : memory.c.obj (.text.split)
                  000026bc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000271e    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002780    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  000027e0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000283c    0000005c            : printf.c.obj (.text.printf)
                  00002898    00000058     libsysbm.a : hostread.c.obj (.text.HOSTread)
                  000028f0    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00002948    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000029a0    00000058            : _printfi.c.obj (.text._pconv_f)
                  000029f8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002a4e    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00002aa0    00000052            : _printfi.c.obj (.text._ecpy)
                  00002af2    00000002            : _lock.c.obj (.text._nop)
                  00002af4    00000050     libsysbm.a : close.c.obj (.text.close)
                  00002b44    0000004c     app_motor_usart.o (.text.Contrl_Speed)
                  00002b90    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002bda    00000002     --HOLE-- [fill = 0]
                  00002bdc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002c24    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  00002c6c    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  00002cb4    00000044     usart.o (.text.UART0_IRQHandler)
                  00002cf8    00000044     app_motor_usart.o (.text.send_wheel_diameter)
                  00002d3c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002d7c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002dbc    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002dfc    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00002e3c    00000040     delay.o (.text.delay_ms)
                  00002e7c    00000040     empty.o (.text.main)
                  00002ebc    00000040     app_motor_usart.o (.text.send_motor_deadzone)
                  00002efc    00000040     app_motor_usart.o (.text.send_motor_type)
                  00002f3c    00000040     app_motor_usart.o (.text.send_pulse_line)
                  00002f7c    00000040     app_motor_usart.o (.text.send_pulse_phase)
                  00002fbc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002ff8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003034    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003070    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000030aa    00000002     --HOLE-- [fill = 0]
                  000030ac    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000030e4    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  0000311c    00000034            : fopen.c.obj (.text.__TI_cleanup)
                  00003150    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  00003184    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  000031b8    00000034     libc.a : exit.c.obj (.text.exit)
                  000031ec    00000034            : getdevice.c.obj (.text.finddevice)
                  00003220    00000030            : _printfi.c.obj (.text._fcpy)
                  00003250    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000327c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000032a8    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  000032d4    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  000032fc    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  00003324    00000028                : write.c.obj (.text.write)
                  0000334c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003374    00000024     usart.o (.text.USART_Init)
                  00003398    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000033bc    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000033de    00000002     --HOLE-- [fill = 0]
                  000033e0    00000020     bsp_motor_usart.o (.text.UART1_IRQHandler)
                  00003400    00000020     usart.o (.text.fputc)
                  00003420    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000343e    00000002     --HOLE-- [fill = 0]
                  00003440    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000345c    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003478    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  00003494    00000018            : sprintf.c.obj (.text._outs)
                  000034ac    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000034c0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000034d4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000034e6    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000034f8    00000010            : wcslen.c.obj (.text.wcslen)
                  00003508    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003518    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003526    00000002     --HOLE-- [fill = 0]
                  00003528    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003536    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  00003544    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003552    0000000e     libsysbm.a : hostrename.c.obj (.text.strlen)
                  00003560    0000000c     libc.a : memory.c.obj (.text.malloc)
                  0000356c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003576    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003580    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003590    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000359a    0000000a            : sprintf.c.obj (.text._outc)
                  000035a4    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000035ac    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000035b4    00000008     libc.a : printf.c.obj (.text._outc)
                  000035bc    00000008            : printf.c.obj (.text._outs)
                  000035c4    00000008            : fseek.c.obj (.text.fseek)
                  000035cc    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  000035d4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000035d8    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000035dc    00000004            : exit.c.obj (.text:abort)

.cinit     0    000037a0    00000090     
                  000037a0    00000066     (.cinit..data.load) [load image, compression = lzss]
                  00003806    00000002     --HOLE-- [fill = 0]
                  00003808    0000000c     (__TI_handler_table)
                  00003814    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000381c    00000010     (__TI_cinit_table)
                  0000382c    00000004     --HOLE-- [fill = 0]

.rodata    0    000035e0    000001c0     
                  000035e0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000036e1    00000016     app_motor_usart.o (.rodata.str1.138129986531886932851)
                  000036f7    00000012     app_motor_usart.o (.rodata.str1.177430081765570553101)
                  00003709    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  0000371a    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  0000372b    00000011     app_motor_usart.o (.rodata.str1.19366273577901094251)
                  0000373c    0000000f     empty.o (.rodata.str1.176633223477948356601)
                  0000374b    0000000e     app_motor_usart.o (.rodata.str1.11867396368620600391)
                  00003759    0000000c     app_motor_usart.o (.rodata.str1.69024998803089298601)
                  00003765    0000000b     app_motor_usart.o (.rodata.str1.157726972120725782571)
                  00003770    0000000b     app_motor_usart.o (.rodata.str1.63670896866782352001)
                  0000377b    00000001     --HOLE-- [fill = 0]
                  0000377c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003786    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00003790    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003792    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00003794    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    00000404     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    00000100     (.common:g_recv_buff)
                  20200a20    00000100     (.common:g_recv_buff_deal)
                  20200b20    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200bc0    00000032     (.common:send_buff)
                  20200bf2    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200bfa    00000001     (.common:g_recv_flag)
                  20200bfb    00000001     --HOLE--
                  20200bfc    00000004     Four_linewalking.o (.bss.APP_IR_PID_Calc.IRTrack_Integral)
                  20200c00    00000004     libc.a : memory.c.obj (.bss.sys_free)

.data      0    20200c04    00000265     UNINITIALIZED
                  20200c04    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200cf4    00000080     usart.o (.data.recv0_buff)
                  20200d74    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200dec    00000050                : host_device.c.obj (.data._stream)
                  20200e3c    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200e44    00000004     Four_linewalking.o (.data.IRR_SPEED)
                  20200e48    00000004     libc.a : defs.c.obj (.data.__TI_ft_end)
                  20200e4c    00000004            : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200e50    00000004            : _lock.c.obj (.data._lock)
                  20200e54    00000004            : _lock.c.obj (.data._unlock)
                  20200e58    00000004     Four_linewalking.o (.data.err)
                  20200e5c    00000004     Four_linewalking.o (.data.pid_output_IRR)
                  20200e60    00000002     app_motor_usart.o (.data.Deal_Control_Rxtemp.step)
                  20200e62    00000002     usart.o (.data.recv0_length)
                  20200e64    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.start_flag)
                  20200e65    00000001     usart.o (.data.recv0_flag)
                  20200e66    00000002     --HOLE--
                  20200e68    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       startup_mspm0g350x_ticlang.o   6       192       0      
       empty.o                        64      15        0      
    +--+------------------------------+-------+---------+---------+
       Total:                         70      207       0      
                                                               
    .\BSP\
       app_motor_usart.o              620     105       566    
       app_motor.o                    388     0         0      
       Four_linewalking.o             276     0         16     
       usart.o                        136     0         131    
       bsp_motor_usart.o              260     0         0      
       delay.o                        64      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1744    105       713    
                                                               
    .\syscfg\
       ti_msp_dl_config.o             448     24        0      
    +--+------------------------------+-------+---------+---------+
       Total:                         448     24        0      
                                                               
    E:/SoftWare/ti/mspm0_sdk_2_02_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         100     0         0      
                                                               
    E:\SoftWare\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       memory.c.obj                   702     0         5      
       defs.c.obj                     0       0         404    
       aeabi_ctype.S.obj              0       257       0      
       fputs.c.obj                    248     0         0      
       setvbuf.c.obj                  224     0         0      
       s_scalbn.c.obj                 216     0         0      
       getdevice.c.obj                160     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       fclose.c.obj                   124     0         0      
       fseek.c.obj                    116     0         0      
       printf.c.obj                   108     0         0      
       _io_perm.c.obj                 100     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       fflush.c.obj                   82      0         0      
       atoi.c.obj                     64      0         0      
       exit.c.obj                     56      0         8      
       autoinit.c.obj                 60      0         0      
       fopen.c.obj                    52      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         7742    291       429    
                                                               
    E:\SoftWare\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104     0         288    
       host_device.c.obj              0       0         200    
       hostrename.c.obj               136     0         0      
       hostlseek.c.obj                108     0         0      
       hostopen.c.obj                 96      0         8      
       hostread.c.obj                 88      0         0      
       hostwrite.c.obj                88      0         0      
       close.c.obj                    80      0         0      
       hostclose.c.obj                72      0         0      
       hostunlink.c.obj               72      0         0      
       unlink.c.obj                   44      0         0      
       lseek.c.obj                    40      0         0      
       write.c.obj                    40      0         0      
       remove.c.obj                   8       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         976     0         496    
                                                               
    E:\SoftWare\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2510    0         0      
                                                               
       Heap:                          0       0         2048   
       Stack:                         0       0         512    
       Linker Generated:              0       138       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   13590   765       4198   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000381c records: 2, size/record: 8, table size: 16
	.data: load addr=000037a0, load size=00000066 bytes, run addr=20200c04, run size=00000265 bytes, compression=lzss
	.bss: load addr=00003814, load size=00000008 bytes, run addr=20200800, run size=00000404 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003808 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00000e8d     00003580     0000357e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000101f  ADC0_IRQHandler               
0000101f  ADC1_IRQHandler               
0000101f  AES_IRQHandler                
000035dc  C$$EXIT                       
000031b1  C$$IO$$                       
0000101f  CANFD0_IRQHandler             
00002b45  Contrl_Speed                  
0000101f  DAC0_IRQHandler               
0000356d  DL_Common_delayCycles         
00002bdd  DL_UART_init                  
000034d5  DL_UART_setClockConfig        
0000101f  DMA_IRQHandler                
00002145  Deal_Control_Rxtemp           
0000101f  Default_Handler               
0000127d  Four_LineWalking              
0000101f  GROUP0_IRQHandler             
0000101f  GROUP1_IRQHandler             
00002c25  HOSTclose                     
0000230d  HOSTlseek                     
00002781  HOSTopen                      
00002899  HOSTread                      
00002379  HOSTrename                    
00002c6d  HOSTunlink                    
000028f1  HOSTwrite                     
0000101f  HardFault_Handler             
0000101f  I2C0_IRQHandler               
0000101f  I2C1_IRQHandler               
20200e44  IRR_SPEED                     
00001e19  Motion_Car_Control            
0000101f  NMI_Handler                   
0000101f  PendSV_Handler                
0000101f  RTC_IRQHandler                
000035d5  Reset_Handler                 
0000101f  SPI0_IRQHandler               
0000101f  SPI1_IRQHandler               
0000101f  SVC_Handler                   
00003251  SYSCFG_DL_GPIO_init           
00002fbd  SYSCFG_DL_SYSCTL_init         
00003441  SYSCFG_DL_SYSTICK_init        
0000222d  SYSCFG_DL_UART_0_init         
0000229d  SYSCFG_DL_UART_1_init         
0000345d  SYSCFG_DL_init                
00002d3d  SYSCFG_DL_initPower           
00001885  Send_Motor_ArrayU8            
000016a9  Set_Motor                     
0000101f  SysTick_Handler               
0000101f  TIMA0_IRQHandler              
0000101f  TIMA1_IRQHandler              
0000101f  TIMG0_IRQHandler              
0000101f  TIMG12_IRQHandler             
0000101f  TIMG6_IRQHandler              
0000101f  TIMG7_IRQHandler              
0000101f  TIMG8_IRQHandler              
00002cb5  UART0_IRQHandler              
000033e1  UART1_IRQHandler              
0000101f  UART2_IRQHandler              
0000101f  UART3_IRQHandler              
00003375  USART_Init                    
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000381c  __TI_CINIT_Base               
0000382c  __TI_CINIT_Limit              
0000382c  __TI_CINIT_Warm               
00003808  __TI_Handler_Table_Base       
00003814  __TI_Handler_Table_Limit      
00003035  __TI_auto_init_nobinit_nopinit
0000311d  __TI_cleanup                  
20200e3c  __TI_cleanup_ptr              
0000204d  __TI_closefile                
000020c9  __TI_decompress_lzss          
000034e7  __TI_decompress_none          
00002a4f  __TI_doflush                  
20200e40  __TI_dtors_ptr                
20200e48  __TI_ft_end                   
00002949  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00003151  __TI_readmsg                  
00000000  __TI_static_base__            
20200b20  __TI_tmpnams                  
00003185  __TI_writemsg                 
000025f3  __TI_wrt_ok                   
00003509  __TI_zero_init                
00000e97  __adddf3                      
00001c0f  __addsf3                      
000035e0  __aeabi_ctype_table_          
000035e0  __aeabi_ctype_table_C         
00002b91  __aeabi_d2iz                  
00000e97  __aeabi_dadd                  
000026bd  __aeabi_dcmpeq                
000026f9  __aeabi_dcmpge                
0000270d  __aeabi_dcmpgt                
000026e5  __aeabi_dcmple                
000026d1  __aeabi_dcmplt                
000014a5  __aeabi_ddiv                  
00001969  __aeabi_dmul                  
00000e8d  __aeabi_dsub                  
20200e4c  __aeabi_errno                 
000035a5  __aeabi_errno_addr            
00002dbd  __aeabi_f2d                   
000030ad  __aeabi_f2iz                  
00001c0f  __aeabi_fadd                  
00001fc9  __aeabi_fdiv                  
00001ea9  __aeabi_fmul                  
00001c05  __aeabi_fsub                  
0000327d  __aeabi_i2d                   
00002ff9  __aeabi_i2f                   
000029f9  __aeabi_idiv                  
0000204b  __aeabi_idiv0                 
000029f9  __aeabi_idivmod               
00002657  __aeabi_ldiv0                 
00003421  __aeabi_llsl                  
00003399  __aeabi_lmul                  
00001f35  __aeabi_memclr                
00001f35  __aeabi_memclr4               
00001f35  __aeabi_memclr8               
000035ad  __aeabi_memcpy                
000035ad  __aeabi_memcpy4               
000035ad  __aeabi_memcpy8               
00003529  __aeabi_memset                
00003529  __aeabi_memset4               
00003529  __aeabi_memset8               
00002d7d  __aeabi_uidiv                 
00002d7d  __aeabi_uidivmod              
000034ad  __aeabi_uldivmod              
00003421  __ashldi3                     
ffffffff  __binit__                     
000024bd  __cmpdf2                      
000014a5  __divdf3                      
00001fc9  __divsf3                      
000024bd  __eqdf2                       
00002dbd  __extendsfdf2                 
00002b91  __fixdfsi                     
000030ad  __fixsfsi                     
0000327d  __floatsidf                   
00002ff9  __floatsisf                   
000021b9  __gedf2                       
000021b9  __gtdf2                       
000024bd  __ledf2                       
000024bd  __ltdf2                       
UNDEFED   __mpu_init                    
00001969  __muldf3                      
00003399  __muldi3                      
00003071  __muldsi3                     
00001ea9  __mulsf3                      
000024bd  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000e8d  __subdf3                      
00001c05  __subsf3                      
00001cdd  __udivmoddi4                  
0000334d  _c_int00_noargs               
20200d74  _device                       
20200c04  _ftable                       
20200e50  _lock                         
00002af3  _nop                          
20200dec  _stream                       
20200000  _sys_memory                   
UNDEFED   _system_post_cinit            
000035d9  _system_pre_init              
20200e54  _unlock                       
000035dd  abort                         
00001391  aligned_alloc                 
00002dfd  atoi                          
ffffffff  binit                         
00002af5  close                         
00002e3d  delay_ms                      
20200e58  err                           
000031b9  exit                          
000031ed  finddevice                    
00003401  fputc                         
000015b1  fputs                         
0000179d  free                          
000027e1  frexp                         
000027e1  frexpl                        
000035c5  fseek                         
000023e5  fseeko                        
20200920  g_recv_buff                   
20200a20  g_recv_buff_deal              
20200bfa  g_recv_flag                   
00002451  getdevice                     
00000000  interruptVectors              
00001b2d  ldexp                         
00001b2d  ldexpl                        
000032fd  lseek                         
00002e7d  main                          
00003561  malloc                        
00001391  memalign                      
000033bd  memccpy                       
00001d7f  memcpy                        
0000271f  memset                        
20200bf2  parmbuf                       
20200e5c  pid_output_IRR                
0000283d  printf                        
20200cf4  recv0_buff                    
20200e65  recv0_flag                    
20200e62  recv0_length                  
000035cd  remove                        
00001b2d  scalbn                        
00001b2d  scalbnl                       
20200bc0  send_buff                     
00002525  send_motor_PID                
00002ebd  send_motor_deadzone           
00002efd  send_motor_type               
00002f3d  send_pulse_line               
00002f7d  send_pulse_phase              
00002cf9  send_wheel_diameter           
00001a4d  setvbuf                       
000030e5  sprintf                       
00001f41  strcmp                        
000032a9  unlink                        
000034f9  wcslen                        
00003325  write                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000e8d  __aeabi_dsub                  
00000e8d  __subdf3                      
00000e97  __adddf3                      
00000e97  __aeabi_dadd                  
0000101f  ADC0_IRQHandler               
0000101f  ADC1_IRQHandler               
0000101f  AES_IRQHandler                
0000101f  CANFD0_IRQHandler             
0000101f  DAC0_IRQHandler               
0000101f  DMA_IRQHandler                
0000101f  Default_Handler               
0000101f  GROUP0_IRQHandler             
0000101f  GROUP1_IRQHandler             
0000101f  HardFault_Handler             
0000101f  I2C0_IRQHandler               
0000101f  I2C1_IRQHandler               
0000101f  NMI_Handler                   
0000101f  PendSV_Handler                
0000101f  RTC_IRQHandler                
0000101f  SPI0_IRQHandler               
0000101f  SPI1_IRQHandler               
0000101f  SVC_Handler                   
0000101f  SysTick_Handler               
0000101f  TIMA0_IRQHandler              
0000101f  TIMA1_IRQHandler              
0000101f  TIMG0_IRQHandler              
0000101f  TIMG12_IRQHandler             
0000101f  TIMG6_IRQHandler              
0000101f  TIMG7_IRQHandler              
0000101f  TIMG8_IRQHandler              
0000101f  UART2_IRQHandler              
0000101f  UART3_IRQHandler              
0000127d  Four_LineWalking              
00001391  aligned_alloc                 
00001391  memalign                      
000014a5  __aeabi_ddiv                  
000014a5  __divdf3                      
000015b1  fputs                         
000016a9  Set_Motor                     
0000179d  free                          
00001885  Send_Motor_ArrayU8            
00001969  __aeabi_dmul                  
00001969  __muldf3                      
00001a4d  setvbuf                       
00001b2d  ldexp                         
00001b2d  ldexpl                        
00001b2d  scalbn                        
00001b2d  scalbnl                       
00001c05  __aeabi_fsub                  
00001c05  __subsf3                      
00001c0f  __addsf3                      
00001c0f  __aeabi_fadd                  
00001cdd  __udivmoddi4                  
00001d7f  memcpy                        
00001e19  Motion_Car_Control            
00001ea9  __aeabi_fmul                  
00001ea9  __mulsf3                      
00001f35  __aeabi_memclr                
00001f35  __aeabi_memclr4               
00001f35  __aeabi_memclr8               
00001f41  strcmp                        
00001fc9  __aeabi_fdiv                  
00001fc9  __divsf3                      
0000204b  __aeabi_idiv0                 
0000204d  __TI_closefile                
000020c9  __TI_decompress_lzss          
00002145  Deal_Control_Rxtemp           
000021b9  __gedf2                       
000021b9  __gtdf2                       
0000222d  SYSCFG_DL_UART_0_init         
0000229d  SYSCFG_DL_UART_1_init         
0000230d  HOSTlseek                     
00002379  HOSTrename                    
000023e5  fseeko                        
00002451  getdevice                     
000024bd  __cmpdf2                      
000024bd  __eqdf2                       
000024bd  __ledf2                       
000024bd  __ltdf2                       
000024bd  __nedf2                       
00002525  send_motor_PID                
000025f3  __TI_wrt_ok                   
00002657  __aeabi_ldiv0                 
000026bd  __aeabi_dcmpeq                
000026d1  __aeabi_dcmplt                
000026e5  __aeabi_dcmple                
000026f9  __aeabi_dcmpge                
0000270d  __aeabi_dcmpgt                
0000271f  memset                        
00002781  HOSTopen                      
000027e1  frexp                         
000027e1  frexpl                        
0000283d  printf                        
00002899  HOSTread                      
000028f1  HOSTwrite                     
00002949  __TI_ltoa                     
000029f9  __aeabi_idiv                  
000029f9  __aeabi_idivmod               
00002a4f  __TI_doflush                  
00002af3  _nop                          
00002af5  close                         
00002b45  Contrl_Speed                  
00002b91  __aeabi_d2iz                  
00002b91  __fixdfsi                     
00002bdd  DL_UART_init                  
00002c25  HOSTclose                     
00002c6d  HOSTunlink                    
00002cb5  UART0_IRQHandler              
00002cf9  send_wheel_diameter           
00002d3d  SYSCFG_DL_initPower           
00002d7d  __aeabi_uidiv                 
00002d7d  __aeabi_uidivmod              
00002dbd  __aeabi_f2d                   
00002dbd  __extendsfdf2                 
00002dfd  atoi                          
00002e3d  delay_ms                      
00002e7d  main                          
00002ebd  send_motor_deadzone           
00002efd  send_motor_type               
00002f3d  send_pulse_line               
00002f7d  send_pulse_phase              
00002fbd  SYSCFG_DL_SYSCTL_init         
00002ff9  __aeabi_i2f                   
00002ff9  __floatsisf                   
00003035  __TI_auto_init_nobinit_nopinit
00003071  __muldsi3                     
000030ad  __aeabi_f2iz                  
000030ad  __fixsfsi                     
000030e5  sprintf                       
0000311d  __TI_cleanup                  
00003151  __TI_readmsg                  
00003185  __TI_writemsg                 
000031b1  C$$IO$$                       
000031b9  exit                          
000031ed  finddevice                    
00003251  SYSCFG_DL_GPIO_init           
0000327d  __aeabi_i2d                   
0000327d  __floatsidf                   
000032a9  unlink                        
000032fd  lseek                         
00003325  write                         
0000334d  _c_int00_noargs               
00003375  USART_Init                    
00003399  __aeabi_lmul                  
00003399  __muldi3                      
000033bd  memccpy                       
000033e1  UART1_IRQHandler              
00003401  fputc                         
00003421  __aeabi_llsl                  
00003421  __ashldi3                     
00003441  SYSCFG_DL_SYSTICK_init        
0000345d  SYSCFG_DL_init                
000034ad  __aeabi_uldivmod              
000034d5  DL_UART_setClockConfig        
000034e7  __TI_decompress_none          
000034f9  wcslen                        
00003509  __TI_zero_init                
00003529  __aeabi_memset                
00003529  __aeabi_memset4               
00003529  __aeabi_memset8               
00003561  malloc                        
0000356d  DL_Common_delayCycles         
000035a5  __aeabi_errno_addr            
000035ad  __aeabi_memcpy                
000035ad  __aeabi_memcpy4               
000035ad  __aeabi_memcpy8               
000035c5  fseek                         
000035cd  remove                        
000035d5  Reset_Handler                 
000035d9  _system_pre_init              
000035dc  C$$EXIT                       
000035dd  abort                         
000035e0  __aeabi_ctype_table_          
000035e0  __aeabi_ctype_table_C         
00003808  __TI_Handler_Table_Base       
00003814  __TI_Handler_Table_Limit      
0000381c  __TI_CINIT_Base               
0000382c  __TI_CINIT_Limit              
0000382c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  _sys_memory                   
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20200920  g_recv_buff                   
20200a20  g_recv_buff_deal              
20200b20  __TI_tmpnams                  
20200bc0  send_buff                     
20200bf2  parmbuf                       
20200bfa  g_recv_flag                   
20200c04  _ftable                       
20200cf4  recv0_buff                    
20200d74  _device                       
20200dec  _stream                       
20200e3c  __TI_cleanup_ptr              
20200e40  __TI_dtors_ptr                
20200e44  IRR_SPEED                     
20200e48  __TI_ft_end                   
20200e4c  __aeabi_errno                 
20200e50  _lock                         
20200e54  _unlock                       
20200e58  err                           
20200e5c  pid_output_IRR                
20200e62  recv0_length                  
20200e65  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[230 symbols]
