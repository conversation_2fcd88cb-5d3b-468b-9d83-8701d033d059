#ifndef __OLED_H
#define __OLED_H

#include "ti_msp_dl_config.h"
#include <stdint.h>

// OLED引脚定义 - 使用软件I2C
// 选择未被占用的GPIO引脚 (避开编码器PA14/PA15/PA24/PA25, 电机PWM, 串口PA10/PA11)
#define OLED_SCL_PORT    GPIOA
#define OLED_SCL_PIN     DL_GPIO_PIN_2   // PA2 - I2C时钟线
#define OLED_SDA_PORT    GPIOA
#define OLED_SDA_PIN     DL_GPIO_PIN_3   // PA3 - I2C数据线

// OLED控制宏定义
#define OLED_SCL_HIGH    DL_GPIO_setPins(OLED_SCL_PORT, OLED_SCL_PIN)
#define OLED_SCL_LOW     DL_GPIO_clearPins(OLED_SCL_PORT, OLED_SCL_PIN)
#define OLED_SDA_HIGH    DL_GPIO_setPins(OLED_SDA_PORT, OLED_SDA_PIN)
#define OLED_SDA_LOW     DL_GPIO_clearPins(OLED_SDA_PORT, OLED_SDA_PIN)
#define OLED_SDA_READ    DL_GPIO_readPins(OLED_SDA_PORT, OLED_SDA_PIN)

// OLED参数定义
#define OLED_WIDTH       128    // OLED宽度
#define OLED_HEIGHT      64     // OLED高度
#define OLED_PAGES       8      // OLED页数
#define OLED_I2C_ADDR    0x78   // OLED I2C地址 (0x3C << 1)

// OLED命令定义
#define OLED_CMD_SET_CONTRAST           0x81
#define OLED_CMD_DISPLAY_ALL_ON_RESUME  0xA4
#define OLED_CMD_DISPLAY_ALL_ON         0xA5
#define OLED_CMD_NORMAL_DISPLAY         0xA6
#define OLED_CMD_INVERT_DISPLAY         0xA7
#define OLED_CMD_DISPLAY_OFF            0xAE
#define OLED_CMD_DISPLAY_ON             0xAF
#define OLED_CMD_SET_DISPLAY_OFFSET     0xD3
#define OLED_CMD_SET_COMPINS            0xDA
#define OLED_CMD_SET_VCOM_DETECT        0xDB
#define OLED_CMD_SET_DISPLAY_CLK_DIV    0xD5
#define OLED_CMD_SET_PRECHARGE          0xD9
#define OLED_CMD_SET_MULTIPLEX          0xA8
#define OLED_CMD_SET_LOW_COLUMN         0x00
#define OLED_CMD_SET_HIGH_COLUMN        0x10
#define OLED_CMD_SET_START_LINE         0x40
#define OLED_CMD_MEMORY_MODE            0x20
#define OLED_CMD_COLUMN_ADDR            0x21
#define OLED_CMD_PAGE_ADDR              0x22
#define OLED_CMD_COM_SCAN_INC           0xC0
#define OLED_CMD_COM_SCAN_DEC           0xC8
#define OLED_CMD_SEG_REMAP              0xA0
#define OLED_CMD_CHARGE_PUMP            0x8D
#define OLED_CMD_EXTERNAL_VCC           0x01
#define OLED_CMD_SWITCH_CAP_VCC         0x02

// 字体大小定义
typedef enum {
    OLED_FONT_6x8 = 0,
    OLED_FONT_8x16
} OLED_FontSize_t;

// 函数声明
void OLED_Init(void);
void OLED_Clear(void);
void OLED_Display(void);
void OLED_SetPos(uint8_t x, uint8_t y);
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t chr, OLED_FontSize_t size);
void OLED_ShowString(uint8_t x, uint8_t y, char *str, OLED_FontSize_t size);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t size);
void OLED_ShowSignedNum(uint8_t x, uint8_t y, int32_t num, uint8_t len, OLED_FontSize_t size);
void OLED_ShowHexNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t size);
void OLED_ShowBinNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t size);
void OLED_DrawPoint(uint8_t x, uint8_t y);
void OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2);
void OLED_DrawRectangle(uint8_t x, uint8_t y, uint8_t w, uint8_t h);
void OLED_DrawCircle(uint8_t x, uint8_t y, uint8_t r);

// 编码器速度显示专用函数
void OLED_ShowEncoderSpeed(int speed1, int speed2);
void OLED_ShowEncoderInfo(void);

// 内部函数声明
void OLED_I2C_Init(void);
void OLED_I2C_Start(void);
void OLED_I2C_Stop(void);
void OLED_I2C_SendByte(uint8_t dat);
uint8_t OLED_I2C_ReceiveByte(void);
void OLED_WriteCmd(uint8_t cmd);
void OLED_WriteData(uint8_t data);
void OLED_Delay_us(uint32_t us);
uint32_t OLED_Pow(uint8_t m, uint8_t n);

#endif /* __OLED_H */
