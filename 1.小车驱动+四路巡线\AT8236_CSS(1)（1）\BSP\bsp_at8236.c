#include "bsp_at8236.h"

// 初始化电机PWM定时器 - 双驱动版本
void init_motor(void)
{
	DL_TimerA_startCounter(PWM_L1_INST);  // 启动左侧电机PWM
	DL_TimerA_startCounter(PWM_R1_INST);  // 启动右侧电机PWM
}


// 左侧电机控制函数
// motor_speed: 0-1000, dir: 1=正转, 0=反转
void L1_control(uint16_t motor_speed, uint8_t dir)
{
	if(dir)  // 正转
	{
		DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
		DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_1_INDEX);
	}
	else     // 反转
	{
		DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_0_INDEX);
		DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}
}

// 右侧电机控制函数
// motor_speed: 0-1000, dir: 1=正转, 0=反转
void R1_control(uint16_t motor_speed, uint8_t dir)
{
	if(dir)  // 正转
	{
		DL_TimerA_setCaptureCompareValue(PWM_R1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
		DL_TimerA_setCaptureCompareValue(PWM_R1_INST, 0, DL_TIMER_CC_1_INDEX);
	}
	else     // 反转
	{
		DL_TimerA_setCaptureCompareValue(PWM_R1_INST, 0, DL_TIMER_CC_0_INDEX);
		DL_TimerA_setCaptureCompareValue(PWM_R1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}
}

// 停止所有电机
void motor_stop(void)
{
	L1_control(0, 0);
	R1_control(0, 0);
}

// 前进 - 两个电机同向转动
void motor_forward(uint16_t speed)
{
	L1_control(speed, 1);  // 左电机正转
	R1_control(speed, 1);  // 右电机正转
}

// 后退 - 两个电机反向转动
void motor_backward(uint16_t speed)
{
	L1_control(speed, 0);  // 左电机反转
	R1_control(speed, 0);  // 右电机反转
}

// 左转 - 右电机转动，左电机停止或反转
void motor_turn_left(uint16_t speed)
{
	L1_control(0, 0);      // 左电机停止
	R1_control(speed, 1);  // 右电机正转
}

// 右转 - 左电机转动，右电机停止或反转
void motor_turn_right(uint16_t speed)
{
	L1_control(speed, 1);  // 左电机正转
	R1_control(0, 0);      // 右电机停止
}