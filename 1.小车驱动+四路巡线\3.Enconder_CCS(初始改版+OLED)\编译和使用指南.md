# 编码器OLED显示项目 - 编译和使用指南

## 🚀 快速开始

### **1. 硬件连接**
```
OLED模块 (SSD1306):
VCC  → 3.3V
GND  → GND
SCL  → PA2
SDA  → PA3
```

### **2. 项目文件**
确保以下文件已添加到项目中：
- ✅ `BSP/OLED.h` - OLED驱动头文件
- ✅ `BSP/OLED.c` - OLED驱动实现
- ✅ `main.c` - 已修改，包含OLED初始化和显示

### **3. 编译步骤**
1. 在Code Composer Studio中打开项目
2. 右键项目 → Refresh (刷新项目)
3. 确认新增文件已显示在项目树中
4. 点击 Build (编译) 按钮
5. 编译成功后下载到开发板

### **4. 运行效果**
- **启动**: OLED显示系统信息2秒
- **运行**: 实时显示编码器速度和RPM
- **串口**: 同时输出数据到串口 (115200bps)

## 📺 显示效果预览

### **启动画面**
```
┌─────────────────┐
│ MSPM0G3507      │
│ Encoder Test    │
│                 │
│ L1: PA14/PA15   │
│ L2: PA24/PA25   │
│                 │
│ Ready...        │
└─────────────────┘
```

### **运行画面**
```
┌─────────────────┐
│ Encoder Speed   │
│ Period: 10ms    │
│                 │
│ L1:  123 pps    │
│ L2: -456 pps    │
│                 │
│ RPM1:  738      │
│ RPM2: -2736     │
└─────────────────┘
```

## 🔧 如果编译出错

### **常见错误1: 找不到OLED.h**
```
解决方法:
1. 确认OLED.h和OLED.c文件在BSP文件夹中
2. 右键项目 → Refresh
3. 检查#include "OLED.h"路径是否正确
```

### **常见错误2: GPIO函数未定义**
```
解决方法:
1. 确认ti_msp_dl_config.h已包含
2. 检查SDK版本是否匹配
3. 重新生成配置文件
```

### **常见错误3: 链接错误**
```
解决方法:
1. 确认OLED.c已添加到项目编译列表
2. 清理项目后重新编译 (Project → Clean)
3. 检查函数声明和定义是否匹配
```

## 📊 测试验证

### **1. OLED显示测试**
- ✅ 上电后应显示启动信息
- ✅ 2秒后切换到速度显示
- ✅ 数字应清晰可读

### **2. 编码器测试**
- ✅ 手动转动编码器，数值应变化
- ✅ 不同方向转动，数值正负应不同
- ✅ 停止转动，数值应归零

### **3. 串口测试**
- ✅ 连接串口助手 (115200bps)
- ✅ 应看到与OLED相同的数据
- ✅ 每300ms更新一次

## 🎯 功能验证清单

| 功能项目 | 验证方法 | 预期结果 |
|---------|---------|---------|
| OLED初始化 | 上电观察 | 显示启动信息 |
| 编码器L1 | 转动L1编码器 | L1数值变化 |
| 编码器L2 | 转动L2编码器 | L2数值变化 |
| 方向检测 | 正反转编码器 | 正负值变化 |
| 串口输出 | 连接串口助手 | 数据正常输出 |
| 实时更新 | 观察显示 | 每300ms更新 |

## 🛠️ 自定义修改

### **修改显示内容**
编辑 `OLED_ShowEncoderSpeed()` 函数：
```c
// 在OLED.c中找到此函数，可修改显示格式
void OLED_ShowEncoderSpeed(int speed1, int speed2)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Your Title", OLED_FONT_6x8);
    // 添加自定义显示内容
}
```

### **修改更新频率**
在 `main.c` 中修改延时：
```c
delay_ms(300);  // 改为其他值，如100ms或500ms
```

### **修改编码器分辨率**
在 `OLED.c` 中修改RPM计算：
```c
int rpm1 = (speed1 * 600) / 100;  // 100改为实际PPR值
```

## 📞 技术支持

### **调试技巧**
1. **使用串口输出调试**: 在关键位置添加printf输出
2. **检查中断状态**: 确认编码器中断正常触发
3. **分步测试**: 先测试OLED显示，再测试编码器

### **性能优化**
1. **减少OLED刷新频率**: 避免频繁全屏刷新
2. **优化I2C时序**: 根据需要调整延时
3. **内存使用**: 注意显示缓冲区大小

## 🎉 完成！

按照以上步骤，您的编码器项目现在应该具备了OLED实时显示功能。

**主要优势**:
- 🔍 **实时监测**: 无需电脑即可查看编码器状态
- 📊 **数据直观**: 清晰的数字显示和单位标注  
- 🔄 **双路显示**: 同时监测两路编码器
- 🎯 **便于调试**: 现场测试更加方便

如有问题，请检查硬件连接和编译配置，大部分问题都可以通过重新检查连接和重新编译解决。
