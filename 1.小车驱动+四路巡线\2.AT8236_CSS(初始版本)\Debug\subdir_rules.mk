################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
build-335187217: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/CCS/ti/ccs1271/ccs/utils/sysconfig_1.20.0/sysconfig_cli.bat" --script "W:/CCS/2.AT8236_CSS/empty.syscfg" -o "." -s "W:/MSPM0G3507/mspm0_sdk_1_30_00_03/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-335187217 ../empty.syscfg
device.opt: build-335187217
device.cmd.genlibs: build-335187217
ti_msp_dl_config.c: build-335187217
ti_msp_dl_config.h: build-335187217
Event.dot: build-335187217

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/CCS/ti/ccs1271/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"W:/CCS/2.AT8236_CSS/BSP" -I"W:/CCS/2.AT8236_CSS" -I"W:/CCS/2.AT8236_CSS/Debug" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/third_party/CMSIS/Core/Include" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/CCS/ti/ccs1271/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"W:/CCS/2.AT8236_CSS/BSP" -I"W:/CCS/2.AT8236_CSS" -I"W:/CCS/2.AT8236_CSS/Debug" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/third_party/CMSIS/Core/Include" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/CCS/ti/ccs1271/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"W:/CCS/2.AT8236_CSS/BSP" -I"W:/CCS/2.AT8236_CSS" -I"W:/CCS/2.AT8236_CSS/Debug" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/third_party/CMSIS/Core/Include" -I"W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


