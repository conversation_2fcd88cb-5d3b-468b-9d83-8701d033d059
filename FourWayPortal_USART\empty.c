#include "ti_msp_dl_config.h"
#include "delay.h"
#include "usart.h"
#include "app_motor_usart.h"
#include "Four_linewalking.h"
#include "app_motor.h"

#define MOTOR_TYPE 5   //1:520��� 2:310��� 3:��������TT��� 4:TTֱ�����ٵ�� 5:L��520���
                       //1:520 motor 2:310 motor 3:speed code disc TT motor 4:TT DC reduction motor 5:L type 520 motor

int main(void)
{	
	USART_Init();
	printf("please wait...");
    
    Set_Motor(MOTOR_TYPE);
    
    //�޸ĵ��PID������Ĳ�����Ϊ����310�������õģ�����������Ҫ�Լ������޸�
    //Modify the motor PID, the parameters here are configured for the 4WD 310 chassis, other chassis need to test and modify their own!
	send_motor_PID(1.9,0.2,0.8);
    
    delay_ms(100);

	while(1)
	{
		Four_LineWalking();//��·Ѳ�ߣ�������	Four-way line patrol, start!
	}
	
}
