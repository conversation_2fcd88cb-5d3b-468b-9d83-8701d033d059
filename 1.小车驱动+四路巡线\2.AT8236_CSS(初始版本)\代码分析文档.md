# AT8236 CCS开发环境代码详细分析文档

## 项目概述

本项目是基于TI MSPM0G350X微控制器的AT8236电机驱动控制系统，使用Code Composer Studio (CCS)开发环境。项目主要实现了四路PWM控制的电机驱动功能。

## 硬件平台信息

- **微控制器**: MSPM0G350X
- **封装**: LQFP-64(PM)
- **开发环境**: Code Composer Studio (CCS)
- **SDK版本**: mspm0_sdk@1.30.00.03
- **工具版本**: 1.19.0+3426

## 文件结构分析

```
2.AT8236_CSS/
├── BSP/                    # 板级支持包
│   ├── bsp_at8236.c       # AT8236驱动实现
│   └── bsp_at8236.h       # AT8236驱动头文件
├── Debug/                  # 编译输出目录
├── main.c                  # 主程序文件
├── empty.syscfg           # 系统配置文件
└── targetConfigs/         # 目标配置文件
```

## 主要模块详细分析

### 1. 主函数模块 (main.c)

#### 功能概述
主函数模块是整个程序的入口点，负责系统初始化和主循环控制。

#### 代码分析

**延时函数实现**:
```c
void delay_ms(unsigned int ms) 
{
    unsigned int i, j;
    for (i = 0; i < ms; i++) 
    {
        for (j = 0; j < 8000; j++) 
        {
            __asm__("nop");
        }
    }
}
```
- 实现软件延时功能
- 使用双重循环结构
- 内层循环8000次，配合nop指令实现精确延时
- 延时精度依赖于系统时钟频率

**主函数实现**:
```c
int main(void)
{
    SYSCFG_DL_init();        // 系统配置初始化
    init_motor();            // 电机初始化
    
    while (1) 
    {    
        L1_control(600,0);   // 左侧电机1控制：速度600，反转
        L2_control(700,0);   // 左侧电机2控制：速度700，反转
        R1_control(400,1);   // 右侧电机1控制：速度400，正转
        R2_control(300,1);   // 右侧电机2控制：速度300，正转
    }
}
```

#### 实现功能
1. **系统初始化**: 调用SYSCFG_DL_init()进行系统级配置
2. **电机初始化**: 启动四路PWM定时器
3. **电机控制**: 在主循环中持续控制四个电机的速度和方向

### 2. BSP_AT8236模块分析

#### 2.1 头文件 (bsp_at8236.h)

**功能声明**:
```c
void init_motor(void);                                    // 电机初始化
void L1_control(uint16_t motor_speed,uint8_t dir);       // 左侧电机1控制
void L2_control(uint16_t motor_speed,uint8_t dir);       // 左侧电机2控制
void R1_control(uint16_t motor_speed,uint8_t dir);       // 右侧电机1控制
void R2_control(uint16_t motor_speed,uint8_t dir);       // 右侧电机2控制
```

#### 2.2 实现文件 (bsp_at8236.c)

**电机初始化函数**:
```c
void init_motor(void)
{
    DL_TimerA_startCounter(PWM_L1_INST);  // 启动左侧电机1的PWM定时器
    DL_TimerA_startCounter(PWM_L2_INST);  // 启动左侧电机2的PWM定时器
    DL_TimerA_startCounter(PWM_R1_INST);  // 启动右侧电机1的PWM定时器
    DL_TimerA_startCounter(PWM_R2_INST);  // 启动右侧电机2的PWM定时器
}
```

**电机控制函数实现原理**:
每个电机控制函数都采用相同的控制逻辑：
- 使用两个PWM通道控制电机方向
- 通过设置不同通道的占空比实现正反转控制
- dir参数：1表示正转，0表示反转

**L1电机控制示例**:
```c
void L1_control(uint16_t motor_speed,uint8_t dir)
{
    if(dir)  // 正转
    {
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_1_INDEX);
    }
    else     // 反转
    {
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_0_INDEX);
        DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
    }
}
```

#### BSP模块实现的功能
1. **PWM定时器管理**: 启动和控制四路PWM定时器
2. **电机方向控制**: 通过双PWM通道实现正反转
3. **速度控制**: 通过PWM占空比控制电机转速
4. **模块化设计**: 每个电机独立控制，便于扩展和维护

### 3. Empty.syscfg配置模块分析

#### 3.1 PWM配置概述
系统配置了4个PWM模块，分别对应4个电机：

| PWM模块 | 定时器 | 引脚配置 | 功能 |
|---------|--------|----------|------|
| PWM_L1  | TIMG0  | PA12, PA13 | 左侧电机1 |
| PWM_L2  | TIMG7  | PA26, PA27 | 左侧电机2 |
| PWM_R1  | TIMG6  | PA21, PA22 | 右侧电机1 |
| PWM_R2  | TIMA0  | PA0, PA1   | 右侧电机2 |

#### 3.2 PWM参数配置
```javascript
clockDivider: 8          // 时钟分频器
clockPrescale: 40        // 时钟预分频
```

**时钟计算**:
- 系统时钟: 32MHz (CPUCLK_FREQ = 32000000)
- PWM时钟频率: 32MHz / 8 / 40 = 100kHz
- 这个配置提供了良好的PWM分辨率和控制精度

#### 3.3 引脚配置特点
1. **双通道配置**: 每个PWM模块配置两个输出通道
2. **GPIO方向**: 所有PWM引脚配置为输出方向
3. **引脚分布**: 合理分布在不同的GPIO端口，避免引脚冲突

## 系统工作原理

### 1. 电机驱动原理
本系统采用双PWM控制方式：
- **正转**: 通道0输出PWM信号，通道1输出低电平
- **反转**: 通道0输出低电平，通道1输出PWM信号
- **停止**: 两个通道都输出低电平

### 2. 速度控制原理
- 速度参数范围: 0-1000
- 通过调节PWM占空比实现速度控制
- 占空比 = motor_speed / 1000

### 3. 系统时序
1. 系统上电后调用SYSCFG_DL_init()初始化硬件
2. 调用init_motor()启动所有PWM定时器
3. 进入主循环，持续控制四个电机

## 技术特点与优势

### 1. 模块化设计
- BSP层封装了硬件相关操作
- 主程序只需调用高层接口
- 便于移植和维护

### 2. 硬件资源利用
- 充分利用MSPM0G350X的多个定时器资源
- 合理分配GPIO引脚
- 高效的PWM控制方式

### 3. 实时性保证
- 主循环直接控制，响应速度快
- PWM硬件生成，波形稳定
- 无复杂的中断处理，系统稳定

## 潜在改进建议

### 1. 功能扩展
- 添加电机速度反馈控制
- 实现PID闭环控制
- 增加电机保护功能

### 2. 代码优化
- 添加参数范围检查
- 实现更精确的软件延时
- 增加错误处理机制

### 3. 系统架构
- 考虑使用中断驱动的控制方式
- 实现任务调度机制
- 添加通信接口支持

## 总结

本项目实现了一个基于MSPM0G350X的四路电机PWM控制系统，具有以下特点：

1. **完整的硬件抽象**: BSP层提供了良好的硬件封装
2. **灵活的控制方式**: 支持独立的速度和方向控制
3. **稳定的系统架构**: 简洁的主循环设计保证了系统稳定性
4. **良好的扩展性**: 模块化设计便于功能扩展

该系统适用于小型机器人、自动化设备等需要多电机协调控制的应用场景。
