# LQ_MSPM0GX_LIB_V1.0.0 代码详细分析报告

## 📋 项目概述

### 硬件平台
- **微控制器**: MSPM0G3507 (LQFP-64封装)
- **SDK版本**: mspm0_sdk@2.04.00.06
- **开发环境**: Code Composer Studio + TI SysConfig
- **时钟频率**: 32MHz (使用16MHz外部晶振 + PLL)

### 系统架构
这是一个**双路电机驱动系统**，集成了8路循迹传感器、编码器反馈、舵机控制、OLED显示、串口通信等功能，主要用于智能小车的循迹控制。

## 🔧 硬件配置分析 (empty.syscfg)

### 1. 时钟系统配置
```
外部晶振: 16MHz (PA5/PA6)
系统时钟: 32MHz (通过PLL倍频)
总线时钟: 32MHz
PWM时钟: 分频后用于不同PWM模块
```

### 2. 引脚分配详细说明

#### **电机驱动系统 (双路)**
```
PWM_Motor (TIMA1):
- PB2: 电机1 PWM输出 (CCP0)
- PB3: 电机2 PWM输出 (CCP1)

电机方向控制:
- PB4: Motor_IO1 (电机1方向控制)
- PB5: Motor_IO2 (电机2方向控制)
```

#### **编码器接口 (双路)**
```
左编码器:
- PA7: L_A (A相，上升沿中断)
- PA3: L_B (B相)

右编码器:
- PA8: R_A (A相，上升沿中断)  
- PB7: R_B (B相)
```

#### **8路循迹传感器系统**
```
ADC输入:
- PA27: ADC0_CH0 (模拟输入)

多路选择控制:
- PA26: S0 (选择位0)
- PA25: S1 (选择位1)
- PA24: S2 (选择位2)

通道映射:
S2 S1 S0 | 通道
0  0  0  | 通道1
0  0  1  | 通道2
0  1  0  | 通道3
0  1  1  | 通道4
1  0  0  | 通道5
1  0  1  | 通道6
1  1  0  | 通道7
1  1  1  | 通道8
```

#### **舵机控制系统 (5路)**
```
PWM_Servo_A0 (TIMA0):
- PB9:  舵机1 (CCP1)
- PB12: 舵机4 (CCP2)
- PB13: 舵机5 (CCP3)

PWM_Servo_G0 (TIMG0):
- PB10: 舵机2 (CCP0)
- PB11: 舵机3 (CCP1)
```

#### **通信接口**
```
UART0 (115200bps):
- PA10: TX
- PA11: RX
```

#### **显示和交互**
```
OLED (SPI模拟):
- PA17: OLED_CK (时钟)
- PA16: OLED_DI (数据)
- PB21: OLED_RST (复位)
- PB23: OLED_DC (数据/命令)
- PB22: OLED_CS (片选)

LED:
- PA15: LED0

蜂鸣器:
- PA28: Buzzer

按键:
- PB14: K1
- PB15: K2  
- PB16: K3

拨码开关:
- PB6: SW1
- PB8: SW2
```

#### **传感器接口**
```
LSM6DSR (SPI):
- PA12: LSM6DSR_SCL
- PA14: LSM6DSR_MISO
- PA13: LSM6DSR_MOSI
- PA2:  LSM6DSR_CS
```

#### **调试接口**
```
SWD:
- PA20: SWCLK
- PA19: SWDIO
```

## 📁 代码模块详细分析

### 1. 主程序模块 (User/empty.c)

#### **PID结构体定义**
```c
typedef struct {
    float KP, KI, KD;        // PID参数
    float iError;            // 当前误差
    float LastError;         // 上次误差  
    float PrevError;         // 前次误差
} PID;
```

#### **核心控制变量**
```c
// PID控制器实例
PID Left_MOTOR_PID, Right_MOTOR_PID;    // 电机速度PID
PID Turn_PID_ele, Angle_PID;            // 转向PID
PID UP_LOW_Servo_PID, LEFT_RIGHT_Servo_PID; // 舵机PID

// 控制参数
float Turn_ele[2] = {1.5, 7.5};         // 转向PD参数
float Left_MOTOR[3] = {25, 20, 0};      // 左电机PID参数
float Right_MOTOR[3] = {25, 20, 0};     // 右电机PID参数

// 传感器数据
unsigned char Adc_Value[8];             // 8路循迹传感器值
float L_error, R_error;                 // 左右误差
float eleValue, eleOut;                 // 循迹误差和输出
```

#### **主要控制算法**

**1. 位置式PID控制**
```c
float PlacePID_Control(PID*sptr, float NowPiont, float SetPoint, float *Turn_PID)
{
    sptr->iError = SetPoint - NowPiont;
    Output = sptr->KP * sptr->iError + sptr->KD * (sptr->iError - sptr->LastError);
    sptr->LastError = sptr->iError;
    return Output;
}
```

**2. 增量式PID控制**
```c
float PID_Realize(PID*sptr, float ActualSpeed, float SetSpeed, float *MOTOR_PID)
{
    sptr->iError = SetSpeed - ActualSpeed;
    Increase = sptr->KP * (sptr->iError - sptr->LastError)
             + sptr->KI * sptr->iError  
             + sptr->KD * (sptr->iError - 2*sptr->LastError + sptr->PrevError);
    return Increase;
}
```

**3. 循迹控制算法**
```c
void Dir_Control()
{
    // 1. 获取8路传感器数据并归一化
    AD_Get();
    
    // 2. 计算左右误差
    L_error = Adc_error[0]*0.4 + Adc_error[1]*0.35 + Adc_error[2]*0.25 + Adc_error[3]*0.2;
    R_error = Adc_error[4]*0.2 + Adc_error[5]*0.25 + Adc_error[6]*0.35 + Adc_error[7]*0.4;
    
    // 3. 计算循迹偏差
    eleValue = L_error - R_error;
    
    // 4. PID控制计算
    eleOut = PlacePID_Control(&Turn_PID_ele, eleValue, elemid, Turn_ele);
    
    // 5. 差速控制
    if(eleOut >= 0) {  // 需要右转
        Left_High_Speed = High_Speed * (1 - Angle);
        Right_High_Speed = High_Speed * (1 + Angle*0.2);
    } else {  // 需要左转
        Left_High_Speed = High_Speed * (1 + Angle*0.2);
        Right_High_Speed = High_Speed * (1 - Angle);
    }
}
```

**4. 电机控制**
```c
void Motor_Control()
{
    // 增量式PID速度控制
    LEFT_MOTOR_Duty += PID_Realize(&Left_MOTOR_PID, LQ_encoder_L, Left_High_Speed, Left_MOTOR);
    RIGHT_MOTOR_Duty += PID_Realize(&Right_MOTOR_PID, LQ_encoder_R, Right_High_Speed, Right_MOTOR);
    
    // 限幅保护
    LEFT_MOTOR_Duty = range_protect(LEFT_MOTOR_Duty, -MOTOR_MAX, MOTOR_MAX);
    RIGHT_MOTOR_Duty = range_protect(RIGHT_MOTOR_Duty, -MOTOR_MAX, MOTOR_MAX);
    
    // 输出到电机
    Motor_Ctrl((int32_t)LEFT_MOTOR_Duty, (int32_t)RIGHT_MOTOR_Duty);
}
```

#### **定时器中断处理 (5ms周期)**
```c
void TIMER_0_INST_IRQHandler(void)
{
    if(DL_TimerG_getPendingInterrupt(TIMER_0_INST) == DL_TIMER_IIDX_ZERO)
    {
        // 1. 获取编码器瞬时速度
        LQ_encoder_L = LQ_encoder_L_Last;
        LQ_encoder_R = LQ_encoder_R_Last;
        LQ_encoder_L_Last = 0;
        LQ_encoder_R_Last = 0;
        
        // 2. 执行循迹控制
        Dir_Control();
        
        // 3. 执行电机控制
        Motor_Control();
        
        // 4. 按键处理
        Key_Control();
    }
}
```

### 2. 8路循迹传感器模块 (LQ_tracking.c/h)

#### **模块功能**
- 通过3个GPIO控制8路模拟多路选择器
- 单ADC通道循环采集8路传感器数据
- 数据滤波和归一化处理

#### **核心函数分析**

**1. 通道选择控制**
```c
void Tracking_IO_Set(unsigned char S2, unsigned char S1, unsigned char S0)
{
    if (S2 == 0) Tracking_S2_LOW; else Tracking_S2_HIGH;
    if (S1 == 0) Tracking_S1_LOW; else Tracking_S1_HIGH;
    if (S0 == 0) Tracking_S0_LOW; else Tracking_S0_HIGH;
}
```

**2. 单次ADC采集**
```c
unsigned int Tracking_Adc_once(void)
{
    DL_ADC12_startConversion(ADC_Tracking_INST);
    while (false == Tracking_Adc_Check) __WFE();  // 等待转换完成
    Adc_Result = DL_ADC12_getMemResult(ADC_Tracking_INST, ADC_Tracking_ADCMEM_ADC0_CH0);
    Tracking_Adc_Check = false;
    return Adc_Result;
}
```

**3. 单通道数据获取(带滤波)**
```c
unsigned int Tracking_Value_once(unsigned char ch)
{
    unsigned char num_samples = 5;      // 采样次数
    unsigned char discard_samples = 3;  // 丢弃前3次
    
    for (unsigned char i = 0; i < num_samples; i++) {
        // 设置通道
        Tracking_IO_Set(/* 根据ch设置S2,S1,S0 */);
        
        // ADC采集并转换为0-100范围
        data = Tracking_Adc_once() * 0.02442;
        data = data >= 100 ? 100 : (data <= 0 ? 0 : data);
        
        // 累加有效数据
        if (i >= discard_samples) sum += data;
    }
    
    return sum / (num_samples - discard_samples);
}
```

**4. 8路数据批量获取**
```c
void Tracking_Value_Acquire()
{
    for (unsigned char i = 0; i < 8; i++) {
        LQ_Tracking_Value[i] = Tracking_Value_once(i + 1);
    }
}
```

### 3. 双路电机驱动模块 (LQ_motor.c/h)

#### **硬件配置**
- 使用DRV8701E双路电机驱动芯片
- PWM频率: 12.5kHz
- PWM占空比范围: 0-10000

#### **核心函数**

**1. PWM设置**
```c
void PWM_Set(GPTIMER_Regs *INST, int32_t Duty, DL_TIMER_CC_INDEX IDX)
{
    Duty = (int32_t)(Duty / (float)PWM_Denom);  // 转换为实际计数值
    Duty = Duty >= PWM_Period ? PWM_Period : (Duty <= 0 ? 0 : Duty);  // 限幅
    DL_TimerG_setCaptureCompareValue(INST, (int32_t)Duty, IDX);
}
```

**2. 双电机控制**
```c
void Motor_Ctrl(int32_t Duty2, int32_t Duty1)
{
    // 电机1方向控制
    if(Duty1 >= 0) {
        DL_GPIO_setPins(Motor_PORT, Motor_Motor_IO1_PIN);  // 正转
    } else {
        Duty1 = -Duty1;
        DL_GPIO_clearPins(Motor_PORT, Motor_Motor_IO1_PIN);  // 反转
    }
    
    // 电机2方向控制
    if(Duty2 >= 0) {
        DL_GPIO_setPins(Motor_PORT, Motor_Motor_IO2_PIN);  // 正转
    } else {
        Duty2 = -Duty2;
        DL_GPIO_clearPins(Motor_PORT, Motor_Motor_IO2_PIN);  // 反转
    }
    
    // 设置PWM占空比
    PWM_Set(PWM_Motor_INST, (int32_t)Duty1, GPIO_PWM_Motor_C0_IDX);
    PWM_Set(PWM_Motor_INST, (int32_t)Duty2, GPIO_PWM_Motor_C1_IDX);
}
```

### 4. 编码器模块 (LQ_encoder.c/h)

#### **工作原理**
- A/B相正交编码器
- A相上升沿触发外部中断
- 根据B相电平判断转向
- 定时器中断周期性读取速度

#### **核心变量**
```c
int LQ_encoder_L = 0, LQ_encoder_R = 0;           // 当前周期速度
int LQ_encoder_L_Last = 0, LQ_encoder_R_Last = 0; // 累计计数值
```

#### **中断处理**
```c
void GROUP1_IRQHandler(void)  // GPIO中断
{
    GPIO_Interrup_flag = DL_GPIO_getEnabledInterruptStatus(Encoder_L_A_PORT, Encoder_L_A_PIN|Encoder_R_A_PIN);
    
    // 左编码器处理
    if((GPIO_Interrup_flag & Encoder_L_A_PIN) == Encoder_L_A_PIN) {
        if(DL_GPIO_readPins(Encoder_L_B_PORT, Encoder_L_B_PIN) == 0) {
            LQ_encoder_L_Last++;  // 正转
        } else {
            LQ_encoder_L_Last--;  // 反转
        }
    }
    
    // 右编码器处理
    if((GPIO_Interrup_flag & Encoder_R_A_PIN) == Encoder_R_A_PIN) {
        if(DL_GPIO_readPins(Encoder_R_B_PORT, Encoder_R_B_PIN) == 0) {
            LQ_encoder_R_Last--;  // 反转(方向相反)
        } else {
            LQ_encoder_R_Last++;  // 正转
        }
    }
    
    DL_GPIO_clearInterruptStatus(Encoder_L_A_PORT, Encoder_L_A_PIN|Encoder_R_A_PIN);
}
```

## 🚗 小车驱动类型分析

### **确认：这是双路电机驱动系统**

**硬件证据：**
1. **电机PWM输出**: 只有2路 (PB2, PB3)
2. **电机方向控制**: 只有2路 (PB4, PB5)  
3. **编码器接口**: 只有2路 (左编码器PA7/PA3, 右编码器PA8/PB7)
4. **电机控制函数**: `Motor_Ctrl(Duty2, Duty1)` - 只控制2个电机

**软件证据：**
1. **PID控制器**: 只有`Left_MOTOR_PID`和`Right_MOTOR_PID`两个
2. **速度变量**: 只有`Left_High_Speed`和`Right_High_Speed`
3. **编码器变量**: 只有`LQ_encoder_L`和`LQ_encoder_R`

### **差速转向控制原理**
```c
if(eleOut >= 0) {  // 右转
    Left_High_Speed = High_Speed * (1 - Angle);    // 左轮减速
    Right_High_Speed = High_Speed * (1 + Angle*0.2); // 右轮微加速
} else {  // 左转  
    Left_High_Speed = High_Speed * (1 + Angle*0.2);  // 左轮微加速
    Right_High_Speed = High_Speed * (1 - Angle);     // 右轮减速
}
```

## 🔄 八路改四路循迹的影响分析

### **当前8路循迹配置**
```c
#define ADC_NUM 8
unsigned char LQ_Tracking_Value[8];  // 8路传感器数据
unsigned char Adc_Value[8];          // 8路归一化数据

// 加权计算左右误差
L_error = Adc_error[0]*0.4 + Adc_error[1]*0.35 + Adc_error[2]*0.25 + Adc_error[3]*0.2;
R_error = Adc_error[4]*0.2 + Adc_error[5]*0.25 + Adc_error[6]*0.35 + Adc_error[7]*0.4;
```

### **改为4路的影响评估**

#### **✅ 不影响的部分**
1. **电机驱动系统** - 完全不受影响
2. **编码器系统** - 完全不受影响  
3. **PID控制算法** - 完全不受影响
4. **舵机控制** - 完全不受影响
5. **串口通信** - 完全不受影响
6. **OLED显示** - 完全不受影响

#### **🔧 需要修改的部分**

**1. 硬件配置修改**
```c
// 原来需要3个控制引脚选择8路
// 改为4路只需要2个控制引脚
#define ADC_NUM 4  // 改为4

// 通道选择逻辑简化
// S1 S0 | 通道
// 0  0  | 通道1
// 0  1  | 通道2  
// 1  0  | 通道3
// 1  1  | 通道4
```

**2. 软件修改**
```c
// 数组大小修改
unsigned char LQ_Tracking_Value[4];  // 改为4
unsigned char Adc_Value[4];          // 改为4

// 误差计算算法修改
L_error = Adc_error[0]*0.6 + Adc_error[1]*0.4;  // 左侧2路
R_error = Adc_error[2]*0.4 + Adc_error[3]*0.6;  // 右侧2路

// 循环修改
for (unsigned char i = 0; i < 4; i++) {  // 改为4
    LQ_Tracking_Value[i] = Tracking_Value_once(i + 1);
}
```

**3. 通道选择函数修改**
```c
void Tracking_IO_Set(unsigned char S1, unsigned char S0)  // 去掉S2
{
    if (S1 == 0) Tracking_S1_LOW; else Tracking_S1_HIGH;
    if (S0 == 0) Tracking_S0_LOW; else Tracking_S0_HIGH;
}

unsigned int Tracking_Value_once(unsigned char ch)
{
    // 简化通道选择
    if (ch == 1) Tracking_IO_Set(0, 0);  // 通道1
    if (ch == 2) Tracking_IO_Set(0, 1);  // 通道2
    if (ch == 3) Tracking_IO_Set(1, 0);  // 通道3
    if (ch == 4) Tracking_IO_Set(1, 1);  // 通道4
    // ...
}
```

### **总结**

**改为4路循迹对整体代码的影响很小**：

1. **核心控制算法不变** - PID控制、电机驱动、编码器反馈等核心功能完全不受影响
2. **只需修改循迹模块** - 主要是`LQ_tracking.c`中的数组大小、循环次数和误差计算
3. **硬件简化** - 可以节省1个GPIO引脚(S2)，降低硬件复杂度
4. **性能提升** - 4路采集比8路采集速度更快，实时性更好
5. **精度影响** - 循迹精度会有所降低，但对于一般应用仍然足够

**建议的4路传感器布局**：
```
[L2] [L1] [R1] [R2]
 左外 左内 右内 右外
```

这样的布局可以保持良好的循迹性能，同时简化硬件和软件实现。

### 5. 舵机控制模块 (LQ_servo.c/h)

#### **技术规格**
- 支持5路180°舵机控制
- PWM频率: 50Hz (20ms周期)
- 脉宽范围: 0.5ms-2.5ms (对应0°-180°)
- 占空比范围: 250-1250 (对应0.5ms-2.5ms)

#### **舵机映射关系**
```c
舵机1: PWM_Servo_A0_INST, CCP1, PB9
舵机2: PWM_Servo_G0_INST, CCP0, PB10
舵机3: PWM_Servo_G0_INST, CCP1, PB11
舵机4: PWM_Servo_A0_INST, CCP2, PB12
舵机5: PWM_Servo_A0_INST, CCP3, PB13
```

#### **核心控制函数**
```c
void Servo_Ctrl(unsigned char ch, unsigned int Duty)
{
    // 占空比限幅 (250-1250)
    Duty = Duty >= 1250 ? 1250 : (Duty <= 250 ? 250 : Duty);

    // 根据通道号设置对应PWM
    switch(ch) {
        case 1: DL_TimerG_setCaptureCompareValue(PWM_Servo_A0_INST, Duty, GPIO_PWM_Servo_A0_C1_IDX); break;
        case 2: DL_TimerG_setCaptureCompareValue(PWM_Servo_G0_INST, Duty, GPIO_PWM_Servo_G0_C0_IDX); break;
        case 3: DL_TimerG_setCaptureCompareValue(PWM_Servo_G0_INST, Duty, GPIO_PWM_Servo_G0_C1_IDX); break;
        case 4: DL_TimerG_setCaptureCompareValue(PWM_Servo_A0_INST, Duty, GPIO_PWM_Servo_A0_C2_IDX); break;
        case 5: DL_TimerG_setCaptureCompareValue(PWM_Servo_A0_INST, Duty, GPIO_PWM_Servo_A0_C3_IDX); break;
    }
}
```

#### **角度转换关系**
```c
// 角度到占空比的转换
// 0°   → 250  (0.5ms)
// 45°  → 500  (1.0ms)
// 90°  → 750  (1.5ms)
// 135° → 1000 (2.0ms)
// 180° → 1250 (2.5ms)

// 转换公式: Duty = 250 + (angle / 180.0) * 1000
```

### 6. 串口通信模块 (LQ_usart.c/h + bsp_uart.c/h)

#### **通信配置**
```c
UART0: PA10(TX)/PA11(RX), 115200bps, 8N1
缓冲区大小: 256字节
中断驱动接收
```

#### **数据结构**
```c
typedef struct {
    bool flag;      // 目标检测标志
    float X;        // X坐标偏差
    float y;        // Y坐标偏差
    uint16_t size;  // 目标大小
} OMV_STRUCT;
```

#### **协议解析**
```c
void Uart_getdata(void)
{
    if (uart0_flag) {
        // 解析目标检测数据: "O:x:1.23y:4.56s:789"
        if (sscanf((char *)uart0_Buffer, "O:x:%fy:%fs:%d", &Value[0], &Value[1], &uart_size[0]) == 3) {
            OMV.flag = true;
            OMV.X = (float)Value[0];
            OMV.y = (float)Value[1];
            OMV.size = (uint16_t)uart_size[0];
        }
        // 解析无目标数据: "N"
        else if (strstr((char *)uart0_Buffer, "N")) {
            OMV.flag = false;
        }

        memset(uart0_Buffer, 0, sizeof(uart0_Buffer));
        uart0_flag = 0;
    }
}
```

#### **中断处理**
```c
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_getPendingInterrupt(UART_0_INST)) {
        case DL_UART_IIDX_RX:
            uart_data = DL_UART_Main_receiveData(UART_0_INST);

            // 检测帧结束符
            if (uart_data == '\n' || uart_data == '\r') {
                uart0_flag = 1;
                rx0Index = 0;
                tx0Index = 0;
                Uart_getdata();  // 立即解析数据
            }
            // 存储数据到环形缓冲区
            else if (((rx0Index + 1) % BUFFER_SIZE) != tx0Index) {
                uart0_Buffer[rx0Index] = uart_data;
                rx0Index = (rx0Index + 1) % BUFFER_SIZE;
            }
            break;
    }
}
```

### 7. 其他支持模块

#### **GPIO控制模块 (LQ_gpio.c/h)**
```c
// LED控制
#define LED_ON      DL_GPIO_clearPins(LED_PORT, LED_LED0_PIN)
#define LED_OFF     DL_GPIO_setPins(LED_PORT, LED_LED0_PIN)
#define LED_TOGGLE  DL_GPIO_togglePins(LED_PORT, LED_LED0_PIN)

// 蜂鸣器控制
#define Buzz_ON     DL_GPIO_setPins(BUZZ_PORT, BUZZ_Buzzer_PIN)
#define Buzz_OFF    DL_GPIO_clearPins(BUZZ_PORT, BUZZ_Buzzer_PIN)
```

#### **按键处理模块 (LQ_key.c/h)**
```c
// 按键状态检测
uint8_t Key_Scan(void) {
    if (!DL_GPIO_readPins(Key_PORT, Key_K1_PIN)) return 1;  // K1按下
    if (!DL_GPIO_readPins(Key_PORT, Key_K2_PIN)) return 2;  // K2按下
    if (!DL_GPIO_readPins(Key_PORT, Key_K3_PIN)) return 3;  // K3按下
    return 0;  // 无按键
}
```

#### **OLED显示模块 (LQ_oled.c/h)**
```c
// 显示函数
void OLED_ShowString(uint8_t Line, uint8_t Column, uint8_t *String, uint8_t FontSize);
void OLED_ShowNum(uint8_t Line, uint8_t Column, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_Refresh(void);  // 刷新显示缓存到屏幕
```

## 🔧 系统工作流程

### **初始化流程**
```
1. SYSCFG_DL_init()     → 系统时钟和GPIO初始化
2. Encoder_Init()       → 编码器中断初始化
3. OLED_Init()         → OLED显示初始化
4. Tracking_Adc_Init() → 循迹ADC初始化
5. bsp_uart_init()     → 串口通信初始化
```

### **主循环 (1ms)**
```
while(1) {
    1. 显示OMV数据到OLED
    2. 获取循迹传感器数据 Tracking_Value_Acquire()
    3. 数据归一化处理 AD_Get()
    4. 显示调试信息
    5. OLED_Refresh()
}
```

### **定时器中断 (5ms)**
```
TIMER_0_INST_IRQHandler() {
    1. 获取编码器瞬时速度
    2. 执行循迹控制 Dir_Control()
    3. 执行电机PID控制 Motor_Control()
    4. 按键处理 Key_Control()
}
```

### **GPIO中断 (编码器)**
```
GROUP1_IRQHandler() {
    1. 检测A相上升沿
    2. 读取B相电平判断方向
    3. 累加编码器计数值
}
```

### **串口中断 (数据接收)**
```
UART_0_INST_IRQHandler() {
    1. 接收字符到缓冲区
    2. 检测帧结束符
    3. 解析OMV数据
}
```

## 📊 性能参数

### **控制精度**
- **循迹精度**: 8路传感器，0-100归一化，加权误差计算
- **速度控制**: 增量式PID，编码器反馈，5ms控制周期
- **转向控制**: 位置式PD控制，差速转向
- **舵机精度**: 0.1°分辨率 (1250-250)/180°

### **实时性能**
- **主控制周期**: 5ms (200Hz)
- **传感器采集**: 8路×5次采样×3次丢弃 = 约2ms
- **串口通信**: 115200bps，中断驱动
- **显示刷新**: 主循环调用，约1ms

### **系统资源**
- **Flash使用**: 约50KB (包含库函数)
- **RAM使用**: 约8KB (包含缓冲区)
- **定时器**: TIMG6(控制), TIMA1(电机PWM), TIMA0+TIMG0(舵机PWM)
- **ADC**: ADC0单通道多路复用
- **GPIO**: 约30个引脚

## 🎯 总结

这是一个功能完整的**双路电机智能小车控制系统**，具有以下特点：

### **✅ 优势**
1. **模块化设计** - 各功能模块独立，便于维护和扩展
2. **双闭环控制** - 位置环(循迹) + 速度环(电机)，控制精度高
3. **多传感器融合** - 8路循迹 + 编码器 + 视觉(OMV)
4. **实时性好** - 5ms控制周期，响应快速
5. **功能丰富** - 循迹、避障、舵机控制、显示、通信等

### **⚠️ 改进建议**
1. **PID参数优化** - 可根据实际测试调整参数
2. **异常处理** - 增加传感器故障检测和处理
3. **数据滤波** - 对编码器和传感器数据进行更好的滤波
4. **功耗优化** - 添加低功耗模式支持

### **🔄 八路改四路影响**
**结论**: 改为4路循迹对整体系统影响很小，只需修改循迹模块的数组大小、循环次数和误差计算算法，核心控制逻辑完全不变。建议采用[L2][L1][R1][R2]的4路布局。
