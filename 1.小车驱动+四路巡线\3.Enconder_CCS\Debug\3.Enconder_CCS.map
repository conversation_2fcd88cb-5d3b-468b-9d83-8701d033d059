******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Wed Jul 24 15:08:45 2024

OUTPUT FILE NAME:   <3.Enconder_CCS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000d75


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000f78  0001f088  R  X
  SRAM                  20200000   00008000  000003ec  00007c14  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000f78   00000f78    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000e18   00000e18    r-x .text
  00000ed8    00000ed8    00000070   00000070    r-- .rodata
  00000f48    00000f48    00000030   00000030    r-- .cinit
20200000    20200000    000001ec   00000000    rw-
  20200000    20200000    000001b0   00000000    rw- .bss
  202001b0    202001b0    0000003c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000e18     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00000408    0000009c     bsp_enconder.o (.text.GROUP1_IRQHandler)
                  000004a4    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000053e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000540    00000098     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000005d8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L1_init)
                  00000658    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L2_init)
                  000006d8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R1_init)
                  00000758    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R2_init)
                  000007d8    00000078     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  00000850    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000008c8    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_MYUART_init)
                  00000938    0000006c     main.o (.text.main)
                  000009a4    00000062     libc.a : memset16.S.obj (.text:memset)
                  00000a06    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000a08    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000a68    00000054     bsp_delay.o (.text.SysTick_Handler)
                  00000abc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000b04    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000b4c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000b8c    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000bc8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000c04    00000038     bsp_usart.o (.text.UART0_IRQHandler)
                  00000c3c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00000c74    00000034     bsp_at8236.o (.text.L1_control)
                  00000ca8    00000034     bsp_at8236.o (.text.L2_control)
                  00000cdc    00000034     bsp_at8236.o (.text.init_motor)
                  00000d10    00000034     bsp_usart.o (.text.uart0_send_string)
                  00000d44    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00000d74    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000d9c    00000022            : memccpy.c.obj (.text.memccpy)
                  00000dbe    00000002     --HOLE-- [fill = 0]
                  00000dc0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000ddc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000df8    0000001c     bsp_delay.o (.text.delay_ms)
                  00000e14    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000e2c    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00000e44    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000e5a    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00000e6c    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000e7e    00000002     --HOLE-- [fill = 0]
                  00000e80    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000e90    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00000e9e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00000eac    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000eb6    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00000ec0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000ec8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000ecc    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000ed0    00000004            : exit.c.obj (.text:abort)
                  00000ed4    00000004     --HOLE-- [fill = 0]

.cinit     0    00000f48    00000030     
                  00000f48    0000000c     (__TI_handler_table)
                  00000f54    0000000b     (.cinit..data.load) [load image, compression = lzss]
                  00000f5f    00000001     --HOLE-- [fill = 0]
                  00000f60    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000f68    00000010     (__TI_cinit_table)

.rodata    0    00000ed8    00000070     
                  00000ed8    00000023     main.o (.rodata.str1.176328792963337784071)
                  00000efb    00000011     libc.a : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  00000f0c    0000000a     ti_msp_dl_config.o (.rodata.gMYUARTConfig)
                  00000f16    00000002     ti_msp_dl_config.o (.rodata.gMYUARTClockConfig)
                  00000f18    00000008     ti_msp_dl_config.o (.rodata.gPWM_L1Config)
                  00000f20    00000008     ti_msp_dl_config.o (.rodata.gPWM_L2Config)
                  00000f28    00000008     ti_msp_dl_config.o (.rodata.gPWM_R1Config)
                  00000f30    00000008     ti_msp_dl_config.o (.rodata.gPWM_R2Config)
                  00000f38    00000003     ti_msp_dl_config.o (.rodata.gPWM_L1ClockConfig)
                  00000f3b    00000003     ti_msp_dl_config.o (.rodata.gPWM_L2ClockConfig)
                  00000f3e    00000003     ti_msp_dl_config.o (.rodata.gPWM_R1ClockConfig)
                  00000f41    00000003     ti_msp_dl_config.o (.rodata.gPWM_R2ClockConfig)
                  00000f44    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b0     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_R2Backup)
                  202000bc    00000078     (.common:gPWM_L2Backup)
                  20200134    00000078     (.common:gPWM_R1Backup)
                  202001ac    00000004     (.common:gpioA)

.data      0    202001b0    0000003c     UNINITIALIZED
                  202001b0    00000023     main.o (.data.buf)
                  202001d3    00000001     bsp_usart.o (.data.uart_data)
                  202001d4    00000004     bsp_delay.o (.data.delay_times)
                  202001d8    00000004     bsp_enconder.o (.data.gEncoderCount_L1)
                  202001dc    00000004     bsp_enconder.o (.data.gEncoderCount_L2)
                  202001e0    00000004     bsp_delay.o (.data.getspeed)
                  202001e4    00000004     main.o (.data.speed2)
                  202001e8    00000004     main.o (.data.speed)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1052   56        428    
       startup_mspm0g350x_ticlang.o   6      192       0      
       main.o                         108    35        43     
    +--+------------------------------+------+---------+---------+
       Total:                         1166   283       471    
                                                              
    .\BSP\
       bsp_enconder.o                 156    0         12     
       bsp_at8236.o                   156    0         0      
       bsp_delay.o                    112    0         8      
       bsp_usart.o                    108    0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         532    0         21     
                                                              
    D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658    17        0      
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     120    0         0      
       memset16.S.obj                 98     0         0      
       sprintf.c.obj                  90     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1302   17        0      
                                                              
    D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memset.S.obj             14     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         88     0         0      
                                                              
    W:/MSPM0G3507/mspm0_sdk_1_30_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     412    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         512    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      47        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3600   347       1004   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000f68 records: 2, size/record: 8, table size: 16
	.data: load addr=00000f54, load size=0000000b bytes, run addr=202001b0, run size=0000003c bytes, compression=lzss
	.bss: load addr=00000f60, load size=00000008 bytes, run addr=20200000, run size=000001b0 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000f48 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000053f  ADC0_IRQHandler                 
0000053f  ADC1_IRQHandler                 
0000053f  AES_IRQHandler                  
00000ed0  C$$EXIT                         
0000053f  CANFD0_IRQHandler               
0000053f  DAC0_IRQHandler                 
00000ead  DL_Common_delayCycles           
000007d9  DL_TimerA_initPWMMode           
00000345  DL_Timer_initPWMMode            
00000dc1  DL_Timer_setCaptCompUpdateMethod
00000e15  DL_Timer_setCaptureCompareOutCtl
00000e81  DL_Timer_setCaptureCompareValue 
00000ddd  DL_Timer_setClockConfig         
00000abd  DL_UART_init                    
00000e5b  DL_UART_setClockConfig          
0000053f  DMA_IRQHandler                  
0000053f  Default_Handler                 
0000053f  GROUP0_IRQHandler               
00000409  GROUP1_IRQHandler               
0000053f  HardFault_Handler               
0000053f  I2C0_IRQHandler                 
0000053f  I2C1_IRQHandler                 
00000c75  L1_control                      
00000ca9  L2_control                      
0000053f  NMI_Handler                     
0000053f  PendSV_Handler                  
0000053f  RTC_IRQHandler                  
00000ec9  Reset_Handler                   
0000053f  SPI0_IRQHandler                 
0000053f  SPI1_IRQHandler                 
0000053f  SVC_Handler                     
00000541  SYSCFG_DL_GPIO_init             
000008c9  SYSCFG_DL_MYUART_init           
000005d9  SYSCFG_DL_PWM_L1_init           
00000659  SYSCFG_DL_PWM_L2_init           
000006d9  SYSCFG_DL_PWM_R1_init           
00000759  SYSCFG_DL_PWM_R2_init           
00000b8d  SYSCFG_DL_SYSCTL_init           
00000d45  SYSCFG_DL_SYSTICK_init          
00000b05  SYSCFG_DL_init                  
00000a09  SYSCFG_DL_initPower             
00000a69  SysTick_Handler                 
0000053f  TIMA0_IRQHandler                
0000053f  TIMA1_IRQHandler                
0000053f  TIMG0_IRQHandler                
0000053f  TIMG12_IRQHandler               
0000053f  TIMG6_IRQHandler                
0000053f  TIMG7_IRQHandler                
0000053f  TIMG8_IRQHandler                
00000c05  UART0_IRQHandler                
0000053f  UART1_IRQHandler                
0000053f  UART2_IRQHandler                
0000053f  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000f68  __TI_CINIT_Base                 
00000f78  __TI_CINIT_Limit                
00000f78  __TI_CINIT_Warm                 
00000f48  __TI_Handler_Table_Base         
00000f54  __TI_Handler_Table_Limit        
00000bc9  __TI_auto_init_nobinit_nopinit  
00000851  __TI_decompress_lzss            
00000e6d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
000000c1  __TI_printfi_minimal            
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000e45  __TI_zero_init_nomemset         
00000a07  __aeabi_idiv0                   
00000ec1  __aeabi_memcpy                  
00000ec1  __aeabi_memcpy4                 
00000ec1  __aeabi_memcpy8                 
00000e91  __aeabi_memset                  
00000e91  __aeabi_memset4                 
00000e91  __aeabi_memset8                 
00000b4d  __aeabi_uidiv                   
00000b4d  __aeabi_uidivmod                
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000d75  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000ecd  _system_pre_init                
00000ed1  abort                           
ffffffff  binit                           
202001b0  buf                             
00000df9  delay_ms                        
202001d4  delay_times                     
202001d8  gEncoderCount_L1                
202001dc  gEncoderCount_L2                
202000bc  gPWM_L2Backup                   
20200134  gPWM_R1Backup                   
20200000  gPWM_R2Backup                   
202001e0  getspeed                        
202001ac  gpioA                           
00000cdd  init_motor                      
00000000  interruptVectors                
00000939  main                            
00000d9d  memccpy                         
000004a5  memcpy                          
000009a5  memset                          
202001e8  speed                           
202001e4  speed2                          
00000c3d  sprintf                         
00000d11  uart0_send_string               
202001d3  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __TI_printfi_minimal            
00000200  __STACK_SIZE                    
00000345  DL_Timer_initPWMMode            
00000409  GROUP1_IRQHandler               
000004a5  memcpy                          
0000053f  ADC0_IRQHandler                 
0000053f  ADC1_IRQHandler                 
0000053f  AES_IRQHandler                  
0000053f  CANFD0_IRQHandler               
0000053f  DAC0_IRQHandler                 
0000053f  DMA_IRQHandler                  
0000053f  Default_Handler                 
0000053f  GROUP0_IRQHandler               
0000053f  HardFault_Handler               
0000053f  I2C0_IRQHandler                 
0000053f  I2C1_IRQHandler                 
0000053f  NMI_Handler                     
0000053f  PendSV_Handler                  
0000053f  RTC_IRQHandler                  
0000053f  SPI0_IRQHandler                 
0000053f  SPI1_IRQHandler                 
0000053f  SVC_Handler                     
0000053f  TIMA0_IRQHandler                
0000053f  TIMA1_IRQHandler                
0000053f  TIMG0_IRQHandler                
0000053f  TIMG12_IRQHandler               
0000053f  TIMG6_IRQHandler                
0000053f  TIMG7_IRQHandler                
0000053f  TIMG8_IRQHandler                
0000053f  UART1_IRQHandler                
0000053f  UART2_IRQHandler                
0000053f  UART3_IRQHandler                
00000541  SYSCFG_DL_GPIO_init             
000005d9  SYSCFG_DL_PWM_L1_init           
00000659  SYSCFG_DL_PWM_L2_init           
000006d9  SYSCFG_DL_PWM_R1_init           
00000759  SYSCFG_DL_PWM_R2_init           
000007d9  DL_TimerA_initPWMMode           
00000851  __TI_decompress_lzss            
000008c9  SYSCFG_DL_MYUART_init           
00000939  main                            
000009a5  memset                          
00000a07  __aeabi_idiv0                   
00000a09  SYSCFG_DL_initPower             
00000a69  SysTick_Handler                 
00000abd  DL_UART_init                    
00000b05  SYSCFG_DL_init                  
00000b4d  __aeabi_uidiv                   
00000b4d  __aeabi_uidivmod                
00000b8d  SYSCFG_DL_SYSCTL_init           
00000bc9  __TI_auto_init_nobinit_nopinit  
00000c05  UART0_IRQHandler                
00000c3d  sprintf                         
00000c75  L1_control                      
00000ca9  L2_control                      
00000cdd  init_motor                      
00000d11  uart0_send_string               
00000d45  SYSCFG_DL_SYSTICK_init          
00000d75  _c_int00_noargs                 
00000d9d  memccpy                         
00000dc1  DL_Timer_setCaptCompUpdateMethod
00000ddd  DL_Timer_setClockConfig         
00000df9  delay_ms                        
00000e15  DL_Timer_setCaptureCompareOutCtl
00000e45  __TI_zero_init_nomemset         
00000e5b  DL_UART_setClockConfig          
00000e6d  __TI_decompress_none            
00000e81  DL_Timer_setCaptureCompareValue 
00000e91  __aeabi_memset                  
00000e91  __aeabi_memset4                 
00000e91  __aeabi_memset8                 
00000ead  DL_Common_delayCycles           
00000ec1  __aeabi_memcpy                  
00000ec1  __aeabi_memcpy4                 
00000ec1  __aeabi_memcpy8                 
00000ec9  Reset_Handler                   
00000ecd  _system_pre_init                
00000ed0  C$$EXIT                         
00000ed1  abort                           
00000f48  __TI_Handler_Table_Base         
00000f54  __TI_Handler_Table_Limit        
00000f68  __TI_CINIT_Base                 
00000f78  __TI_CINIT_Limit                
00000f78  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_R2Backup                   
202000bc  gPWM_L2Backup                   
20200134  gPWM_R1Backup                   
202001ac  gpioA                           
202001b0  buf                             
202001d3  uart_data                       
202001d4  delay_times                     
202001d8  gEncoderCount_L1                
202001dc  gEncoderCount_L2                
202001e0  getspeed                        
202001e4  speed2                          
202001e8  speed                           
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[120 symbols]
