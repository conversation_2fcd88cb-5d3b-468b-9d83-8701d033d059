#include "OLED.h"
#include "bsp_delay.h"
#include <stdio.h>
#include <string.h>

// OLED显示缓冲区
static uint8_t OLED_GRAM[OLED_PAGES][OLED_WIDTH];

// 6x8字体数组
static const uint8_t OLED_F6x8[][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // sp
    {0x00, 0x00, 0x00, 0x2f, 0x00, 0x00}, // !
    {0x00, 0x00, 0x07, 0x00, 0x07, 0x00}, // "
    {0x00, 0x14, 0x7f, 0x14, 0x7f, 0x14}, // #
    {0x00, 0x24, 0x2a, 0x7f, 0x2a, 0x12}, // $
    {0x00, 0x62, 0x64, 0x08, 0x13, 0x23}, // %
    {0x00, 0x36, 0x49, 0x55, 0x22, 0x50}, // &
    {0x00, 0x00, 0x05, 0x03, 0x00, 0x00}, // '
    {0x00, 0x00, 0x1c, 0x22, 0x41, 0x00}, // (
    {0x00, 0x00, 0x41, 0x22, 0x1c, 0x00}, // )
    {0x00, 0x14, 0x08, 0x3E, 0x08, 0x14}, // *
    {0x00, 0x08, 0x08, 0x3E, 0x08, 0x08}, // +
    {0x00, 0x00, 0x00, 0xA0, 0x60, 0x00}, // ,
    {0x00, 0x08, 0x08, 0x08, 0x08, 0x08}, // -
    {0x00, 0x00, 0x60, 0x60, 0x00, 0x00}, // .
    {0x00, 0x20, 0x10, 0x08, 0x04, 0x02}, // /
    {0x00, 0x3E, 0x51, 0x49, 0x45, 0x3E}, // 0
    {0x00, 0x00, 0x42, 0x7F, 0x40, 0x00}, // 1
    {0x00, 0x42, 0x61, 0x51, 0x49, 0x46}, // 2
    {0x00, 0x21, 0x41, 0x45, 0x4B, 0x31}, // 3
    {0x00, 0x18, 0x14, 0x12, 0x7F, 0x10}, // 4
    {0x00, 0x27, 0x45, 0x45, 0x45, 0x39}, // 5
    {0x00, 0x3C, 0x4A, 0x49, 0x49, 0x30}, // 6
    {0x00, 0x01, 0x71, 0x09, 0x05, 0x03}, // 7
    {0x00, 0x36, 0x49, 0x49, 0x49, 0x36}, // 8
    {0x00, 0x06, 0x49, 0x49, 0x29, 0x1E}, // 9
    {0x00, 0x00, 0x36, 0x36, 0x00, 0x00}, // :
    {0x00, 0x00, 0x56, 0x36, 0x00, 0x00}, // ;
    {0x00, 0x08, 0x14, 0x22, 0x41, 0x00}, // <
    {0x00, 0x14, 0x14, 0x14, 0x14, 0x14}, // =
    {0x00, 0x00, 0x41, 0x22, 0x14, 0x08}, // >
    {0x00, 0x02, 0x01, 0x51, 0x09, 0x06}, // ?
    {0x00, 0x32, 0x49, 0x59, 0x51, 0x3E}, // @
    {0x00, 0x7C, 0x12, 0x11, 0x12, 0x7C}, // A
    {0x00, 0x7F, 0x49, 0x49, 0x49, 0x36}, // B
    {0x00, 0x3E, 0x41, 0x41, 0x41, 0x22}, // C
    {0x00, 0x7F, 0x41, 0x41, 0x22, 0x1C}, // D
    {0x00, 0x7F, 0x49, 0x49, 0x49, 0x41}, // E
    {0x00, 0x7F, 0x09, 0x09, 0x09, 0x01}, // F
    {0x00, 0x3E, 0x41, 0x49, 0x49, 0x7A}, // G
    {0x00, 0x7F, 0x08, 0x08, 0x08, 0x7F}, // H
    {0x00, 0x00, 0x41, 0x7F, 0x41, 0x00}, // I
    {0x00, 0x20, 0x40, 0x41, 0x3F, 0x01}, // J
    {0x00, 0x7F, 0x08, 0x14, 0x22, 0x41}, // K
    {0x00, 0x7F, 0x40, 0x40, 0x40, 0x40}, // L
    {0x00, 0x7F, 0x02, 0x0C, 0x02, 0x7F}, // M
    {0x00, 0x7F, 0x04, 0x08, 0x10, 0x7F}, // N
    {0x00, 0x3E, 0x41, 0x41, 0x41, 0x3E}, // O
    {0x00, 0x7F, 0x09, 0x09, 0x09, 0x06}, // P
    {0x00, 0x3E, 0x41, 0x51, 0x21, 0x5E}, // Q
    {0x00, 0x7F, 0x09, 0x19, 0x29, 0x46}, // R
    {0x00, 0x46, 0x49, 0x49, 0x49, 0x31}, // S
    {0x00, 0x01, 0x01, 0x7F, 0x01, 0x01}, // T
    {0x00, 0x3F, 0x40, 0x40, 0x40, 0x3F}, // U
    {0x00, 0x1F, 0x20, 0x40, 0x20, 0x1F}, // V
    {0x00, 0x3F, 0x40, 0x38, 0x40, 0x3F}, // W
    {0x00, 0x63, 0x14, 0x08, 0x14, 0x63}, // X
    {0x00, 0x07, 0x08, 0x70, 0x08, 0x07}, // Y
    {0x00, 0x61, 0x51, 0x49, 0x45, 0x43}, // Z
    {0x00, 0x00, 0x7F, 0x41, 0x41, 0x00}, // [
    {0x00, 0x55, 0x2A, 0x55, 0x2A, 0x55}, // 55
    {0x00, 0x00, 0x41, 0x41, 0x7F, 0x00}, // ]
    {0x00, 0x04, 0x02, 0x01, 0x02, 0x04}, // ^
    {0x00, 0x40, 0x40, 0x40, 0x40, 0x40}, // _
    {0x00, 0x00, 0x01, 0x02, 0x04, 0x00}, // '
    {0x00, 0x20, 0x54, 0x54, 0x54, 0x78}, // a
    {0x00, 0x7F, 0x48, 0x44, 0x44, 0x38}, // b
    {0x00, 0x38, 0x44, 0x44, 0x44, 0x20}, // c
    {0x00, 0x38, 0x44, 0x44, 0x48, 0x7F}, // d
    {0x00, 0x38, 0x54, 0x54, 0x54, 0x18}, // e
    {0x00, 0x08, 0x7E, 0x09, 0x01, 0x02}, // f
    {0x00, 0x18, 0xA4, 0xA4, 0xA4, 0x7C}, // g
    {0x00, 0x7F, 0x08, 0x04, 0x04, 0x78}, // h
    {0x00, 0x00, 0x44, 0x7D, 0x40, 0x00}, // i
    {0x00, 0x40, 0x80, 0x84, 0x7D, 0x00}, // j
    {0x00, 0x7F, 0x10, 0x28, 0x44, 0x00}, // k
    {0x00, 0x00, 0x41, 0x7F, 0x40, 0x00}, // l
    {0x00, 0x7C, 0x04, 0x18, 0x04, 0x78}, // m
    {0x00, 0x7C, 0x08, 0x04, 0x04, 0x78}, // n
    {0x00, 0x38, 0x44, 0x44, 0x44, 0x38}, // o
    {0x00, 0xFC, 0x24, 0x24, 0x24, 0x18}, // p
    {0x00, 0x18, 0x24, 0x24, 0x18, 0xFC}, // q
    {0x00, 0x7C, 0x08, 0x04, 0x04, 0x08}, // r
    {0x00, 0x48, 0x54, 0x54, 0x54, 0x20}, // s
    {0x00, 0x04, 0x3F, 0x44, 0x40, 0x20}, // t
    {0x00, 0x3C, 0x40, 0x40, 0x20, 0x7C}, // u
    {0x00, 0x1C, 0x20, 0x40, 0x20, 0x1C}, // v
    {0x00, 0x3C, 0x40, 0x30, 0x40, 0x3C}, // w
    {0x00, 0x44, 0x28, 0x10, 0x28, 0x44}, // x
    {0x00, 0x1C, 0xA0, 0xA0, 0xA0, 0x7C}, // y
    {0x00, 0x44, 0x64, 0x54, 0x4C, 0x44}, // z
    {0x14, 0x14, 0x14, 0x14, 0x14, 0x14}, // horiz lines
};

/**
 * @brief OLED延时函数
 * @param us 延时微秒数
 */
void OLED_Delay_us(uint32_t us)
{
    // 简单的延时实现，根据32MHz系统时钟调整
    volatile uint32_t i;
    for(i = 0; i < us * 8; i++);
}

/**
 * @brief I2C初始化
 */
void OLED_I2C_Init(void)
{
    // 配置SCL和SDA为输出模式
    DL_GPIO_initDigitalOutputFeatures(OLED_SCL_PORT, OLED_SCL_PIN,
        DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
        DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);

    DL_GPIO_initDigitalOutputFeatures(OLED_SDA_PORT, OLED_SDA_PIN,
        DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
        DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);

    // 设置为高电平
    OLED_SCL_HIGH;
    OLED_SDA_HIGH;
}

/**
 * @brief I2C起始信号
 */
void OLED_I2C_Start(void)
{
    OLED_SDA_HIGH;
    OLED_SCL_HIGH;
    OLED_Delay_us(4);
    OLED_SDA_LOW;
    OLED_Delay_us(4);
    OLED_SCL_LOW;
}

/**
 * @brief I2C停止信号
 */
void OLED_I2C_Stop(void)
{
    OLED_SDA_LOW;
    OLED_SCL_HIGH;
    OLED_Delay_us(4);
    OLED_SDA_HIGH;
    OLED_Delay_us(4);
}

/**
 * @brief I2C发送一个字节
 * @param dat 要发送的数据
 */
void OLED_I2C_SendByte(uint8_t dat)
{
    uint8_t i;
    for(i = 0; i < 8; i++)
    {
        OLED_SCL_LOW;
        if(dat & 0x80)
        {
            OLED_SDA_HIGH;
        }
        else
        {
            OLED_SDA_LOW;
        }
        OLED_Delay_us(2);
        OLED_SCL_HIGH;
        OLED_Delay_us(2);
        dat <<= 1;
    }
    OLED_SCL_LOW;
    OLED_Delay_us(2);
    OLED_SCL_HIGH;
    OLED_Delay_us(2);
    OLED_SCL_LOW;
}

/**
 * @brief 写命令
 * @param cmd 命令
 */
void OLED_WriteCmd(uint8_t cmd)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_I2C_ADDR);
    OLED_I2C_SendByte(0x00);  // 命令模式
    OLED_I2C_SendByte(cmd);
    OLED_I2C_Stop();
}

/**
 * @brief 写数据
 * @param data 数据
 */
void OLED_WriteData(uint8_t data)
{
    OLED_I2C_Start();
    OLED_I2C_SendByte(OLED_I2C_ADDR);
    OLED_I2C_SendByte(0x40);  // 数据模式
    OLED_I2C_SendByte(data);
    OLED_I2C_Stop();
}

/**
 * @brief OLED初始化
 */
void OLED_Init(void)
{
    OLED_I2C_Init();
    delay_ms(100);  // 等待OLED稳定
    
    OLED_WriteCmd(OLED_CMD_DISPLAY_OFF);
    OLED_WriteCmd(OLED_CMD_SET_DISPLAY_CLK_DIV);
    OLED_WriteCmd(0x80);
    OLED_WriteCmd(OLED_CMD_SET_MULTIPLEX);
    OLED_WriteCmd(OLED_HEIGHT - 1);
    OLED_WriteCmd(OLED_CMD_SET_DISPLAY_OFFSET);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(OLED_CMD_SET_START_LINE | 0x00);
    OLED_WriteCmd(OLED_CMD_CHARGE_PUMP);
    OLED_WriteCmd(0x14);
    OLED_WriteCmd(OLED_CMD_MEMORY_MODE);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(OLED_CMD_SEG_REMAP | 0x01);
    OLED_WriteCmd(OLED_CMD_COM_SCAN_DEC);
    OLED_WriteCmd(OLED_CMD_SET_COMPINS);
    OLED_WriteCmd(0x12);
    OLED_WriteCmd(OLED_CMD_SET_CONTRAST);
    OLED_WriteCmd(0xCF);
    OLED_WriteCmd(OLED_CMD_SET_PRECHARGE);
    OLED_WriteCmd(0xF1);
    OLED_WriteCmd(OLED_CMD_SET_VCOM_DETECT);
    OLED_WriteCmd(0x40);
    OLED_WriteCmd(OLED_CMD_DISPLAY_ALL_ON_RESUME);
    OLED_WriteCmd(OLED_CMD_NORMAL_DISPLAY);
    OLED_WriteCmd(OLED_CMD_DISPLAY_ON);
    
    OLED_Clear();
    OLED_Display();
}

/**
 * @brief 清屏
 */
void OLED_Clear(void)
{
    uint8_t i, j;
    for(i = 0; i < OLED_PAGES; i++)
    {
        for(j = 0; j < OLED_WIDTH; j++)
        {
            OLED_GRAM[i][j] = 0x00;
        }
    }
}

/**
 * @brief 更新显示
 */
void OLED_Display(void)
{
    uint8_t i, j;
    
    for(i = 0; i < OLED_PAGES; i++)
    {
        OLED_WriteCmd(0xB0 + i);    // 设置页地址
        OLED_WriteCmd(0x00);        // 设置列低地址
        OLED_WriteCmd(0x10);        // 设置列高地址
        
        for(j = 0; j < OLED_WIDTH; j++)
        {
            OLED_WriteData(OLED_GRAM[i][j]);
        }
    }
}

/**
 * @brief 设置光标位置
 * @param x 列坐标
 * @param y 行坐标
 */
void OLED_SetPos(uint8_t x, uint8_t y)
{
    OLED_WriteCmd(0xB0 + y);
    OLED_WriteCmd(((x & 0xF0) >> 4) | 0x10);
    OLED_WriteCmd((x & 0x0F) | 0x00);
}

/**
 * @brief 显示字符
 * @param x 列坐标
 * @param y 行坐标  
 * @param chr 字符
 * @param size 字体大小
 */
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t chr, OLED_FontSize_t size)
{
    uint8_t c = chr - ' ';
    uint8_t i;
    
    if(x > OLED_WIDTH - 6 || y > OLED_PAGES - 1) return;
    
    if(size == OLED_FONT_6x8)
    {
        for(i = 0; i < 6; i++)
        {
            OLED_GRAM[y][x + i] = OLED_F6x8[c][i];
        }
    }
}

/**
 * @brief 显示字符串
 * @param x 列坐标
 * @param y 行坐标
 * @param str 字符串
 * @param size 字体大小
 */
void OLED_ShowString(uint8_t x, uint8_t y, char *str, OLED_FontSize_t size)
{
    uint8_t j = 0;
    while(str[j])
    {
        OLED_ShowChar(x, y, str[j], size);
        x += 6;
        if(x > 122)
        {
            x = 0;
            y++;
        }
        j++;
    }
}

/**
 * @brief 显示数字
 * @param x 列坐标
 * @param y 行坐标
 * @param num 数字
 * @param len 长度
 * @param size 字体大小
 */
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, OLED_FontSize_t size)
{
    uint8_t t, temp;
    uint8_t enshow = 0;
    
    for(t = 0; t < len; t++)
    {
        temp = (num / OLED_Pow(10, len - t - 1)) % 10;
        if(enshow == 0 && t < (len - 1))
        {
            if(temp == 0)
            {
                OLED_ShowChar(x + 6 * t, y, ' ', size);
                continue;
            }
            else
            {
                enshow = 1;
            }
        }
        OLED_ShowChar(x + 6 * t, y, temp + '0', size);
    }
}

/**
 * @brief 显示有符号数字
 * @param x 列坐标
 * @param y 行坐标
 * @param num 数字
 * @param len 长度
 * @param size 字体大小
 */
void OLED_ShowSignedNum(uint8_t x, uint8_t y, int32_t num, uint8_t len, OLED_FontSize_t size)
{
    uint8_t t, temp;
    uint8_t enshow = 0;
    uint32_t num1;
    
    if(num >= 0)
    {
        OLED_ShowChar(x, y, ' ', size);
        num1 = num;
    }
    else
    {
        OLED_ShowChar(x, y, '-', size);
        num1 = -num;
    }
    
    for(t = 0; t < len; t++)
    {
        temp = (num1 / OLED_Pow(10, len - t - 1)) % 10;
        if(enshow == 0 && t < (len - 1))
        {
            if(temp == 0)
            {
                OLED_ShowChar(x + 6 * (t + 1), y, ' ', size);
                continue;
            }
            else
            {
                enshow = 1;
            }
        }
        OLED_ShowChar(x + 6 * (t + 1), y, temp + '0', size);
    }
}

/**
 * @brief 编码器速度显示专用函数
 * @param speed1 编码器1速度
 * @param speed2 编码器2速度
 */
void OLED_ShowEncoderSpeed(int speed1, int speed2)
{
    // 清屏
    OLED_Clear();
    
    // 显示标题
    OLED_ShowString(0, 0, "Encoder Speed", OLED_FONT_6x8);
    OLED_ShowString(0, 1, "Period: 10ms", OLED_FONT_6x8);
    
    // 显示L1编码器速度
    OLED_ShowString(0, 3, "L1:", OLED_FONT_6x8);
    OLED_ShowSignedNum(24, 3, speed1, 4, OLED_FONT_6x8);
    OLED_ShowString(54, 3, "pps", OLED_FONT_6x8);
    
    // 显示L2编码器速度
    OLED_ShowString(0, 4, "L2:", OLED_FONT_6x8);
    OLED_ShowSignedNum(24, 4, speed2, 4, OLED_FONT_6x8);
    OLED_ShowString(54, 4, "pps", OLED_FONT_6x8);
    
    // 显示转速估算（假设100PPR编码器）
    int rpm1 = (speed1 * 600) / 100;  // RPM = (pps * 60 * 1000ms) / (PPR * 10ms)
    int rpm2 = (speed2 * 600) / 100;
    
    OLED_ShowString(0, 6, "RPM1:", OLED_FONT_6x8);
    OLED_ShowSignedNum(36, 6, rpm1, 4, OLED_FONT_6x8);
    
    OLED_ShowString(0, 7, "RPM2:", OLED_FONT_6x8);
    OLED_ShowSignedNum(36, 7, rpm2, 4, OLED_FONT_6x8);
    
    // 更新显示
    OLED_Display();
}

/**
 * @brief 显示编码器信息
 */
void OLED_ShowEncoderInfo(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "MSPM0G3507", OLED_FONT_6x8);
    OLED_ShowString(0, 1, "Encoder Test", OLED_FONT_6x8);
    OLED_ShowString(0, 3, "L1: PA14/PA15", OLED_FONT_6x8);
    OLED_ShowString(0, 4, "L2: PA24/PA25", OLED_FONT_6x8);
    OLED_ShowString(0, 6, "Ready...", OLED_FONT_6x8);
    OLED_Display();
    delay_ms(2000);
}

/**
 * @brief 计算幂函数
 * @param m 底数
 * @param n 指数
 * @return 结果
 */
uint32_t OLED_Pow(uint8_t m, uint8_t n)
{
    uint32_t result = 1;
    while(n--)
    {
        result *= m;
    }
    return result;
}
