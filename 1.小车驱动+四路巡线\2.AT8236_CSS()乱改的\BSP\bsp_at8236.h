#ifndef __BSP_AT8236_H_
#define __BSP_AT8236_H_

#include "ti_msp_dl_config.h"

// 电机控制函数声明
void init_motor(void);

// 双驱动电机控制 - 只保留L1和R1
void L1_control(uint16_t motor_speed, uint8_t dir);  // 左侧电机控制
void R1_control(uint16_t motor_speed, uint8_t dir);  // 右侧电机控制

// 高级控制函数
void motor_stop(void);                               // 停止所有电机
void motor_forward(uint16_t speed);                  // 前进
void motor_backward(uint16_t speed);                 // 后退
void motor_turn_left(uint16_t speed);                // 左转
void motor_turn_right(uint16_t speed);               // 右转

#endif

