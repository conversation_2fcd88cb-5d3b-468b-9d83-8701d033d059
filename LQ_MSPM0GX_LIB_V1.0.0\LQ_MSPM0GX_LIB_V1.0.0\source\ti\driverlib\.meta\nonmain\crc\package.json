{"_from": "crc", "_id": "crc@4.3.2", "_inBundle": false, "_integrity": "sha512-uGDHf4KLLh2zsHa8D8hIQ1H/HtFQhyHrc0uhHBcoKGol/Xnb+MPYfUMw7cvON6ze/GUESTudKayDcJC5HnJv1A==", "_location": "/crc", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "crc", "name": "crc", "escapedName": "crc", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/crc/-/crc-4.3.2.tgz", "_shasum": "49b7821cbf2cf61dfd079ed93863bbebd5469b9a", "_spec": "crc", "_where": "/home/<USER>", "author": {"name": "<PERSON>", "url": "https://github.com/alex<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/alex<PERSON><PERSON><PERSON>/crc/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Module for calculating Cyclic Redundancy Check (CRC) for Node.js and the browser.", "engines": {"node": ">=12"}, "exports": {".": {"types": "./mjs/index.d.ts", "import": "./mjs/index.js", "require": "./cjs-default-unwrap/index.js"}, "./crc16ccitt": {"types": "./mjs/crc16ccitt.d.ts", "import": "./mjs/crc16ccitt.js", "require": "./cjs-default-unwrap/crc16ccitt.js"}, "./calculators/crc16ccitt": {"types": "./mjs/calculators/crc16ccitt.d.ts", "import": "./mjs/calculators/crc16ccitt.js", "require": "./cjs-default-unwrap/calculators/crc16ccitt.js"}, "./crc16kermit": {"types": "./mjs/crc16kermit.d.ts", "import": "./mjs/crc16kermit.js", "require": "./cjs-default-unwrap/crc16kermit.js"}, "./calculators/crc16kermit": {"types": "./mjs/calculators/crc16kermit.d.ts", "import": "./mjs/calculators/crc16kermit.js", "require": "./cjs-default-unwrap/calculators/crc16kermit.js"}, "./crc16modbus": {"types": "./mjs/crc16modbus.d.ts", "import": "./mjs/crc16modbus.js", "require": "./cjs-default-unwrap/crc16modbus.js"}, "./calculators/crc16modbus": {"types": "./mjs/calculators/crc16modbus.d.ts", "import": "./mjs/calculators/crc16modbus.js", "require": "./cjs-default-unwrap/calculators/crc16modbus.js"}, "./crc16": {"types": "./mjs/crc16.d.ts", "import": "./mjs/crc16.js", "require": "./cjs-default-unwrap/crc16.js"}, "./calculators/crc16": {"types": "./mjs/calculators/crc16.d.ts", "import": "./mjs/calculators/crc16.js", "require": "./cjs-default-unwrap/calculators/crc16.js"}, "./crc16xmodem": {"types": "./mjs/crc16xmodem.d.ts", "import": "./mjs/crc16xmodem.js", "require": "./cjs-default-unwrap/crc16xmodem.js"}, "./calculators/crc16xmodem": {"types": "./mjs/calculators/crc16xmodem.d.ts", "import": "./mjs/calculators/crc16xmodem.js", "require": "./cjs-default-unwrap/calculators/crc16xmodem.js"}, "./crc1": {"types": "./mjs/crc1.d.ts", "import": "./mjs/crc1.js", "require": "./cjs-default-unwrap/crc1.js"}, "./calculators/crc1": {"types": "./mjs/calculators/crc1.d.ts", "import": "./mjs/calculators/crc1.js", "require": "./cjs-default-unwrap/calculators/crc1.js"}, "./crc24": {"types": "./mjs/crc24.d.ts", "import": "./mjs/crc24.js", "require": "./cjs-default-unwrap/crc24.js"}, "./calculators/crc24": {"types": "./mjs/calculators/crc24.d.ts", "import": "./mjs/calculators/crc24.js", "require": "./cjs-default-unwrap/calculators/crc24.js"}, "./crc32": {"types": "./mjs/crc32.d.ts", "import": "./mjs/crc32.js", "require": "./cjs-default-unwrap/crc32.js"}, "./calculators/crc32": {"types": "./mjs/calculators/crc32.d.ts", "import": "./mjs/calculators/crc32.js", "require": "./cjs-default-unwrap/calculators/crc32.js"}, "./crc32mpeg2": {"types": "./mjs/crc32mpeg2.d.ts", "import": "./mjs/crc32mpeg2.js", "require": "./cjs-default-unwrap/crc32mpeg2.js"}, "./calculators/crc32mpeg2": {"types": "./mjs/calculators/crc32mpeg2.d.ts", "import": "./mjs/calculators/crc32mpeg2.js", "require": "./cjs-default-unwrap/calculators/crc32mpeg2.js"}, "./crc81wire": {"types": "./mjs/crc81wire.d.ts", "import": "./mjs/crc81wire.js", "require": "./cjs-default-unwrap/crc81wire.js"}, "./calculators/crc81wire": {"types": "./mjs/calculators/crc81wire.d.ts", "import": "./mjs/calculators/crc81wire.js", "require": "./cjs-default-unwrap/calculators/crc81wire.js"}, "./crc8": {"types": "./mjs/crc8.d.ts", "import": "./mjs/crc8.js", "require": "./cjs-default-unwrap/crc8.js"}, "./calculators/crc8": {"types": "./mjs/calculators/crc8.d.ts", "import": "./mjs/calculators/crc8.js", "require": "./cjs-default-unwrap/calculators/crc8.js"}, "./crc8dvbs2": {"types": "./mjs/crc8dvbs2.d.ts", "import": "./mjs/crc8dvbs2.js", "require": "./cjs-default-unwrap/crc8dvbs2.js"}, "./calculators/crc8dvbs2": {"types": "./mjs/calculators/crc8dvbs2.d.ts", "import": "./mjs/calculators/crc8dvbs2.js", "require": "./cjs-default-unwrap/calculators/crc8dvbs2.js"}, "./crcjam": {"types": "./mjs/crcjam.d.ts", "import": "./mjs/crcjam.js", "require": "./cjs-default-unwrap/crcjam.js"}, "./calculators/crcjam": {"types": "./mjs/calculators/crcjam.d.ts", "import": "./mjs/calculators/crcjam.js", "require": "./cjs-default-unwrap/calculators/crcjam.js"}}, "files": ["calculators", "cjs", "cjs-default-unwrap", "mjs", "*.js", "*.d.ts"], "homepage": "https://github.com/alex<PERSON><PERSON><PERSON>/crc", "keywords": ["crc", "crc16ccitt", "crc16kermit", "crc16modbus", "crc16", "crc16xmodem", "crc1", "crc24", "crc32", "crc81wire", "crc8", "crc8dvbs2", "crcjam"], "license": "MIT", "main": "./cjs-default-unwrap/index.js", "module": "./mjs/index.js", "name": "crc", "peerDependencies": {"buffer": ">=6.0.3"}, "peerDependenciesMeta": {"buffer": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/alexgorbatchev/crc.git"}, "sideEffects": false, "type": "module", "types": "./mjs/index.d.ts", "version": "4.3.2"}