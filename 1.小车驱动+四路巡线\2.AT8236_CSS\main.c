#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"

//�Զ�����ʱ������ȷ��
void delay_ms(unsigned int ms) 
{
    unsigned int i, j;
    // �����Ƕ��ѭ���Ĵ����Ǹ�������Ƶ�ʺͱ��������ɵ�ָ�����ڴ��¼�������ģ�
    // ��Ҫͨ��ʵ�ʲ��Ե������ﵽ�������ʱ��
    for (i = 0; i < ms; i++) 
    {
        for (j = 0; j < 8000; j++) 
        {
            // ��ִ��һ���㹻�������ڿ���Ԥ����ִ��ʱ��Ĳ���
            __asm__("nop"); // "nop" �������޲��������ڴ�����ܹ��У��������һ���򼸸�ʱ������
        }
    }
}     





//������
int main(void)
{
    SYSCFG_DL_init();
	
		init_motor();//�����ʱ����
	
    while (1) 
    {    
			L1_control(600,0);//0-1000 1:��ת
			L2_control(700,0);//0-1000 0:��ת
			
			R1_control(400,1);//0-1000 1:��ת
			R2_control(300,1);//0-1000 0:��ת
       
    }
} 



