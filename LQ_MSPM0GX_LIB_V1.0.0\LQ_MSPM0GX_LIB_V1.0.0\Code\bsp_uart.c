#include "bsp_uart.h"

OMV_STRUCT OMV;

void bsp_uart_init(void)
{
    OMV.flag = false;
    OMV.X = 0.0f;
    OMV.y = 0.0f;
    OMV.size = 0;
    uart_init();
}

void Test_uart(void)
{
    bsp_uart_init();
    OLED_Init(); // 初始化OLED
    char TXT[32];
    while (1)
    {
        sprintf(TXT, "flag:%d", OMV.flag);
        OLED_ShowString(1, 0, (uint8_t *)TXT, 8);
        sprintf(txt, "x: %.2f", OMV.X);
        OLED_ShowString(2, 0, (uint8_t *)txt, 8);
        sprintf(txt, "y: %.2f", OMV.y);
        OLED_ShowString(3, 0, (uint8_t *)txt, 8);
        sprintf(TXT, "size: %d", OMV.size);
        OLED_ShowString(4, 0, (uint8_t *)TXT, 8);
        OLED_Refresh();
        Buzz_ON;
        LED_TOGGLE;
        delay_ms(50);
        /* code */
    }
}

void Uart_getdata(void)
{
    float Value[2];
    int uart_size[2];
    if (uart0_flag)
    {
        if (sscanf((char *)uart0_Buffer, "O:x:%fy:%fs:%d", &Value[0], &Value[1], &uart_size[0]) == 3)
        {
            OMV.flag = true;
            OMV.X = (float)Value[0];
            OMV.y = (float)Value[1];
            OMV.size = (uint16_t)uart_size[0];
        }

        else if (strstr((char *)uart0_Buffer, "N"))
        {
            OMV.flag = false;
        }
        memset(uart0_Buffer, 0, sizeof(uart0_Buffer));
        uart0_flag = 0;
        ;
    }
}