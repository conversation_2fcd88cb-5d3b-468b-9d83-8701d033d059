Dependencies for Project 'LQ_MSPM0GX_LIB', Target 'LQ_MSPM0G3X_LIB': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\Code\bsp_uart.c)(0x6889E5BB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_uart.o -MMD)
I (..\Code\bsp_uart.h)(0x6889E836)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
F (..\Code\bsp_uart.h)(0x6889E836)()
F (..\User\empty.c)(0x688A25D8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\source\include.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/include.o -MMD)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\source\include.h)(0x6889E836)()
F (../empty.syscfg)(0x687EF4E5)()
F (../ti_msp_dl_config.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
F (../ti_msp_dl_config.h)(0x687EF4E5)()
F (startup_mspm0g350x_uvision.s)(0x687EF4E5)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 540" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (..\LQ_device\LQ_gpio.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_gpio.o -MMD)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_gpio.h)(0x6889C632)()
F (..\LQ_device\LQ_key.c)(0x688A2612)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_key.o -MMD)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_key.h)(0x687EF4E5)()
F (..\LQ_device\LQ_oled.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_oled.o -MMD)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_oled.h)(0x687EF4E5)()
F (..\LQ_device\LQ_oledfont.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_oledfont.o -MMD)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)()
F (..\LQ_device\LQ_encoder.c)(0x6889D0E3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_encoder.o -MMD)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_encoder.h)(0x687EF4E5)()
F (..\LQ_device\LQ_motor.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_motor.o -MMD)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_motor.h)(0x687EF4E5)()
F (..\LQ_device\LQ_servo.c)(0x688985AE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_servo.o -MMD)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_servo.h)(0x687EF4E5)()
F (..\LQ_device\LQ_usart.c)(0x6889E7D3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_usart.o -MMD)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_usart.h)(0x6889E7D3)()
F (..\LQ_device\LQ_lsm6dsr.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_lsm6dsr.o -MMD)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)()
F (..\LQ_device\LQ_tracking.c)(0x687EF4E5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../ -I ../source -I ../source/third_party/CMSIS/Core/Include -I ../LQ_device -I ../User -I ../Code

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/lq_tracking.o -MMD)
I (..\LQ_device\LQ_tracking.h)(0x687EF4E5)
I (..\source\include.h)(0x6889E836)
I (..\ti_msp_dl_config.h)(0x687EF4E5)
I (..\source\ti\devices\msp\msp.h)(0x687EF4E5)
I (..\source\ti\devices\DeviceFamily.h)(0x687EF4E5)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x687EF4E5)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x687EF4E5)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\driverlib.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_adc12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aes.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_aesadv.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_comp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_crcp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dac12.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_dma.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_flashctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpamp.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_gpio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_i2c.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_iwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lfss.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_lcd.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mathacl.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_mcan.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_opa.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_rtc_b.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_spi.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_tamperio.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timera.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timer.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_timerg.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_trng.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_uart_main.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_vref.h)(0x687EF4E5)
I (..\source\ti\driverlib\dl_wwdt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687EF4E5)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687EF4E5)
I (..\LQ_device\LQ_gpio.h)(0x6889C632)
I (..\LQ_device\LQ_key.h)(0x687EF4E5)
I (..\LQ_device\LQ_oled.h)(0x687EF4E5)
I (..\LQ_device\LQ_oledfont.h)(0x687EF4E5)
I (..\LQ_device\LQ_encoder.h)(0x687EF4E5)
I (..\LQ_device\LQ_motor.h)(0x687EF4E5)
I (..\LQ_device\LQ_servo.h)(0x687EF4E5)
I (..\LQ_device\LQ_usart.h)(0x6889E7D3)
I (..\LQ_device\LQ_lsm6dsr.h)(0x687EF4E5)
I (..\Code\bsp_uart.h)(0x6889E836)
F (..\LQ_device\LQ_tracking.h)(0x687EF4E5)()
