#include "Four_linewalking.h"
#include "usart.h"

// 简化的循迹模块 - 只实现直线循迹
// 去除复杂的PID控制和转弯逻辑

/**
 * @brief 循迹初始化
 */
void line_follow_init(void)
{
    // 初始化车辆控制
    car_init();
}

/**
 * @brief 获取传感器状态
 * @param l2 左侧外传感器状态
 * @param l1 左侧内传感器状态
 * @param r1 右侧内传感器状态
 * @param r2 右侧外传感器状态
 */
void get_sensor_status(uint8_t *l2, uint8_t *l1, uint8_t *r1, uint8_t *r2)
{
    *l2 = LineWalk_L2_IN;
    *l1 = LineWalk_L1_IN;
    *r1 = LineWalk_R1_IN;
    *r2 = LineWalk_R2_IN;
}

/**
 * @brief 检测是否有黑线
 * @return 1: 检测到黑线, 0: 未检测到黑线
 */
uint8_t detect_black_line(void)
{
    uint8_t l2, l1, r1, r2;
    get_sensor_status(&l2, &l1, &r1, &r2);

    // 任何一个传感器检测到黑线(LOW)就返回1
    if(l2 == LOW || l1 == LOW || r1 == LOW || r2 == LOW)
    {
        return 1;
    }
    return 0;
}

/**
 * @brief 简化的直线循迹函数
 * 只实现基本的直线循迹，不包含复杂转弯
 */
void simple_line_follow(void)
{
    uint8_t l2, l1, r1, r2;
    get_sensor_status(&l2, &l1, &r1, &r2);

    // 调试输出传感器状态
    // printf("L2:%d L1:%d R1:%d R2:%d\r\n", l2, l1, r1, r2);

    // 检测到黑线的情况
    if(detect_black_line())
    {
        // 中间两个传感器都检测到黑线 - 直行
        if(l1 == LOW && r1 == LOW)
        {
            car_forward(LINE_FOLLOW_SPEED);
        }
        // 只有左侧内传感器检测到黑线 - 需要微调右转
        else if(l1 == LOW && r1 == HIGH)
        {
            // 简单的差速控制实现微调
            car_set_speed(LINE_FOLLOW_SPEED/2, LINE_FOLLOW_SPEED, 1, 1);
        }
        // 只有右侧内传感器检测到黑线 - 需要微调左转
        else if(l1 == HIGH && r1 == LOW)
        {
            // 简单的差速控制实现微调
            car_set_speed(LINE_FOLLOW_SPEED, LINE_FOLLOW_SPEED/2, 1, 1);
        }
        // 其他情况 - 继续前进
        else
        {
            car_forward(LINE_FOLLOW_SPEED);
        }
    }
    else
    {
        // 没有检测到黑线 - 停止
        car_stop();
    }

    // 短暂延时
    delay_ms(10);
}

// 保留原有函数名以兼容现有代码
void Four_LineWalking(void)
{
    simple_line_follow();
}