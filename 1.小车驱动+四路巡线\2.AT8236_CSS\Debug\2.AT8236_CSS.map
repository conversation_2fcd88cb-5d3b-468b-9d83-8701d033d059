******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 19:02:47 2025

OUTPUT FILE NAME:   <2.AT8236_CSS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000006a1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  000007a8  00007858  R  X
  SRAM                  20200000   00004000  000003fc  00003c04  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000007a8   000007a8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000006a0   000006a0    r-x .text
  00000760    00000760    00000030   00000030    r-- .rodata
  00000790    00000790    00000018   00000018    r-- .cinit
20200000    20200000    000001fc   00000000    rw-
  20200000    20200000    000001fc   00000000    rw- .bss
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000006a0     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L1_init)
                  00000254    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L2_init)
                  000002e4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R1_init)
                  00000374    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R2_init)
                  00000404    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000458    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000004a8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000004e8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000528    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000564    00000038     main.o (.text.main)
                  0000059c    00000034     bsp_at8236.o (.text.L1_control)
                  000005d0    00000034     bsp_at8236.o (.text.L2_control)
                  00000604    00000034     bsp_at8236.o (.text.R1_control)
                  00000638    00000034     bsp_at8236.o (.text.R2_control)
                  0000066c    00000034     bsp_at8236.o (.text.init_motor)
                  000006a0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000006c8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000006e4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000700    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000718    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000072e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000730    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000740    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  0000074a    00000006     libc.a : exit.c.obj (.text:abort)
                  00000750    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000754    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000758    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000075c    00000004     --HOLE-- [fill = 0]

.cinit     0    00000790    00000018     
                  00000790    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000798    00000004     (__TI_handler_table)
                  0000079c    00000008     (__TI_cinit_table)
                  000007a4    00000004     --HOLE-- [fill = 0]

.rodata    0    00000760    00000030     
                  00000760    00000008     ti_msp_dl_config.o (.rodata.gPWM_L1Config)
                  00000768    00000008     ti_msp_dl_config.o (.rodata.gPWM_L2Config)
                  00000770    00000008     ti_msp_dl_config.o (.rodata.gPWM_R1Config)
                  00000778    00000008     ti_msp_dl_config.o (.rodata.gPWM_R2Config)
                  00000780    00000003     ti_msp_dl_config.o (.rodata.gPWM_L1ClockConfig)
                  00000783    00000003     ti_msp_dl_config.o (.rodata.gPWM_L2ClockConfig)
                  00000786    00000003     ti_msp_dl_config.o (.rodata.gPWM_R1ClockConfig)
                  00000789    00000003     ti_msp_dl_config.o (.rodata.gPWM_R2ClockConfig)
                  0000078c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001fc     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_R2Backup)
                  202000bc    000000a0     (.common:gPWM_L2Backup)
                  2020015c    000000a0     (.common:gPWM_R1Backup)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             868    44        508    
       startup_mspm0g350x_ticlang.o   6      192       0      
       main.o                         56     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         930    236       508    
                                                              
    .\BSP\
       bsp_at8236.o                   260    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         260    0         0      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         366    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         132    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      20        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1692   256       1020   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000079c records: 1, size/record: 8, table size: 8
	.bss: load addr=00000790, load size=00000008 bytes, run addr=20200000, run size=000001fc bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000798 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000072f  ADC0_IRQHandler                 
0000072f  ADC1_IRQHandler                 
0000072f  AES_IRQHandler                  
00000750  C$$EXIT                         
0000072f  CANFD0_IRQHandler               
0000072f  DAC0_IRQHandler                 
00000741  DL_Common_delayCycles           
000000c1  DL_Timer_initFourCCPWMMode      
000006c9  DL_Timer_setCaptCompUpdateMethod
00000701  DL_Timer_setCaptureCompareOutCtl
00000731  DL_Timer_setCaptureCompareValue 
000006e5  DL_Timer_setClockConfig         
0000072f  DMA_IRQHandler                  
0000072f  Default_Handler                 
0000072f  GROUP0_IRQHandler               
0000072f  GROUP1_IRQHandler               
00000751  HOSTexit                        
0000072f  HardFault_Handler               
0000072f  I2C0_IRQHandler                 
0000072f  I2C1_IRQHandler                 
0000059d  L1_control                      
000005d1  L2_control                      
0000072f  NMI_Handler                     
0000072f  PendSV_Handler                  
00000605  R1_control                      
00000639  R2_control                      
0000072f  RTC_IRQHandler                  
00000755  Reset_Handler                   
0000072f  SPI0_IRQHandler                 
0000072f  SPI1_IRQHandler                 
0000072f  SVC_Handler                     
00000459  SYSCFG_DL_GPIO_init             
000001c5  SYSCFG_DL_PWM_L1_init           
00000255  SYSCFG_DL_PWM_L2_init           
000002e5  SYSCFG_DL_PWM_R1_init           
00000375  SYSCFG_DL_PWM_R2_init           
000004a9  SYSCFG_DL_SYSCTL_init           
000004e9  SYSCFG_DL_init                  
00000405  SYSCFG_DL_initPower             
0000072f  SysTick_Handler                 
0000072f  TIMA0_IRQHandler                
0000072f  TIMA1_IRQHandler                
0000072f  TIMG0_IRQHandler                
0000072f  TIMG12_IRQHandler               
0000072f  TIMG6_IRQHandler                
0000072f  TIMG7_IRQHandler                
0000072f  TIMG8_IRQHandler                
0000072f  UART0_IRQHandler                
0000072f  UART1_IRQHandler                
0000072f  UART2_IRQHandler                
0000072f  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
0000079c  __TI_CINIT_Base                 
000007a4  __TI_CINIT_Limit                
000007a4  __TI_CINIT_Warm                 
00000798  __TI_Handler_Table_Base         
0000079c  __TI_Handler_Table_Limit        
00000529  __TI_auto_init_nobinit_nopinit  
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000719  __TI_zero_init_nomemset         
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000006a1  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000759  _system_pre_init                
0000074b  abort                           
ffffffff  binit                           
202000bc  gPWM_L2Backup                   
2020015c  gPWM_R1Backup                   
20200000  gPWM_R2Backup                   
0000066d  init_motor                      
00000000  interruptVectors                
00000565  main                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  DL_Timer_initFourCCPWMMode      
000001c5  SYSCFG_DL_PWM_L1_init           
00000200  __STACK_SIZE                    
00000255  SYSCFG_DL_PWM_L2_init           
000002e5  SYSCFG_DL_PWM_R1_init           
00000375  SYSCFG_DL_PWM_R2_init           
00000405  SYSCFG_DL_initPower             
00000459  SYSCFG_DL_GPIO_init             
000004a9  SYSCFG_DL_SYSCTL_init           
000004e9  SYSCFG_DL_init                  
00000529  __TI_auto_init_nobinit_nopinit  
00000565  main                            
0000059d  L1_control                      
000005d1  L2_control                      
00000605  R1_control                      
00000639  R2_control                      
0000066d  init_motor                      
000006a1  _c_int00_noargs                 
000006c9  DL_Timer_setCaptCompUpdateMethod
000006e5  DL_Timer_setClockConfig         
00000701  DL_Timer_setCaptureCompareOutCtl
00000719  __TI_zero_init_nomemset         
0000072f  ADC0_IRQHandler                 
0000072f  ADC1_IRQHandler                 
0000072f  AES_IRQHandler                  
0000072f  CANFD0_IRQHandler               
0000072f  DAC0_IRQHandler                 
0000072f  DMA_IRQHandler                  
0000072f  Default_Handler                 
0000072f  GROUP0_IRQHandler               
0000072f  GROUP1_IRQHandler               
0000072f  HardFault_Handler               
0000072f  I2C0_IRQHandler                 
0000072f  I2C1_IRQHandler                 
0000072f  NMI_Handler                     
0000072f  PendSV_Handler                  
0000072f  RTC_IRQHandler                  
0000072f  SPI0_IRQHandler                 
0000072f  SPI1_IRQHandler                 
0000072f  SVC_Handler                     
0000072f  SysTick_Handler                 
0000072f  TIMA0_IRQHandler                
0000072f  TIMA1_IRQHandler                
0000072f  TIMG0_IRQHandler                
0000072f  TIMG12_IRQHandler               
0000072f  TIMG6_IRQHandler                
0000072f  TIMG7_IRQHandler                
0000072f  TIMG8_IRQHandler                
0000072f  UART0_IRQHandler                
0000072f  UART1_IRQHandler                
0000072f  UART2_IRQHandler                
0000072f  UART3_IRQHandler                
00000731  DL_Timer_setCaptureCompareValue 
00000741  DL_Common_delayCycles           
0000074b  abort                           
00000750  C$$EXIT                         
00000751  HOSTexit                        
00000755  Reset_Handler                   
00000759  _system_pre_init                
00000798  __TI_Handler_Table_Base         
0000079c  __TI_CINIT_Base                 
0000079c  __TI_Handler_Table_Limit        
000007a4  __TI_CINIT_Limit                
000007a4  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_R2Backup                   
202000bc  gPWM_L2Backup                   
2020015c  gPWM_R1Backup                   
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[91 symbols]
