<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o 2.AT8236_CSS.out -m2.AT8236_CSS.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/2.AT8236_CSS -iC:/Users/<USER>/workspace_ccstheia/2.AT8236_CSS/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=2.AT8236_CSS_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./BSP/bsp_at8236.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6889fbd7</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\2.AT8236_CSS.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6a1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_at8236.o</file>
         <name>bsp_at8236.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\2.AT8236_CSS\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_PWM_L1_init</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.SYSCFG_DL_PWM_L2_init</name>
         <load_address>0x254</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x254</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_PWM_R1_init</name>
         <load_address>0x2e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2e4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_PWM_R2_init</name>
         <load_address>0x374</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x374</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x404</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x404</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x458</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x458</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x4a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4a8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x4e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x528</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x528</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x564</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x564</run_address>
         <size>0x38</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text.L1_control</name>
         <load_address>0x59c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x59c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.L2_control</name>
         <load_address>0x5d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.R1_control</name>
         <load_address>0x604</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x604</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.R2_control</name>
         <load_address>0x638</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x638</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.init_motor</name>
         <load_address>0x66c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x66c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x6c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x6e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x700</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x700</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x718</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x718</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x72e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x72e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x730</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x730</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x740</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x740</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text:abort</name>
         <load_address>0x74a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x74a</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.HOSTexit</name>
         <load_address>0x750</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x750</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x754</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x754</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x758</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x758</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-135">
         <name>.cinit..bss.load</name>
         <load_address>0x790</load_address>
         <readonly>true</readonly>
         <run_address>0x790</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-133">
         <name>__TI_handler_table</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <run_address>0x798</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-134">
         <name>__TI_cinit_table</name>
         <load_address>0x79c</load_address>
         <readonly>true</readonly>
         <run_address>0x79c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-dd">
         <name>.rodata.gPWM_L1Config</name>
         <load_address>0x760</load_address>
         <readonly>true</readonly>
         <run_address>0x760</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.rodata.gPWM_L2Config</name>
         <load_address>0x768</load_address>
         <readonly>true</readonly>
         <run_address>0x768</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.rodata.gPWM_R1Config</name>
         <load_address>0x770</load_address>
         <readonly>true</readonly>
         <run_address>0x770</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.rodata.gPWM_R2Config</name>
         <load_address>0x778</load_address>
         <readonly>true</readonly>
         <run_address>0x778</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.rodata.gPWM_L1ClockConfig</name>
         <load_address>0x780</load_address>
         <readonly>true</readonly>
         <run_address>0x780</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.rodata.gPWM_L2ClockConfig</name>
         <load_address>0x783</load_address>
         <readonly>true</readonly>
         <run_address>0x783</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-de">
         <name>.rodata.gPWM_R1ClockConfig</name>
         <load_address>0x786</load_address>
         <readonly>true</readonly>
         <run_address>0x786</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.rodata.gPWM_R2ClockConfig</name>
         <load_address>0x789</load_address>
         <readonly>true</readonly>
         <run_address>0x789</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b6">
         <name>.common:gPWM_R1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020015c</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b7">
         <name>.common:gPWM_L2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b8">
         <name>.common:gPWM_R2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-137">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_loc</name>
         <load_address>0x12a</load_address>
         <run_address>0x12a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_loc</name>
         <load_address>0x150</load_address>
         <run_address>0x150</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_loc</name>
         <load_address>0x2e8</load_address>
         <run_address>0x2e8</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_loc</name>
         <load_address>0x2fb</load_address>
         <run_address>0x2fb</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x1d22</load_address>
         <run_address>0x1d22</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_loc</name>
         <load_address>0x1dfa</load_address>
         <run_address>0x1dfa</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x221e</load_address>
         <run_address>0x221e</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_loc</name>
         <load_address>0x238a</load_address>
         <run_address>0x238a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x212</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x212</load_address>
         <run_address>0x212</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_abbrev</name>
         <load_address>0x27f</load_address>
         <run_address>0x27f</run_address>
         <size>0x8a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0x309</load_address>
         <run_address>0x309</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x421</load_address>
         <run_address>0x421</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_abbrev</name>
         <load_address>0x483</load_address>
         <run_address>0x483</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x709</load_address>
         <run_address>0x709</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x7b8</load_address>
         <run_address>0x7b8</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x928</load_address>
         <run_address>0x928</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x961</load_address>
         <run_address>0x961</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0xa23</load_address>
         <run_address>0xa23</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0xabb</load_address>
         <run_address>0xabb</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_abbrev</name>
         <load_address>0xae7</load_address>
         <run_address>0xae7</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2716</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2716</load_address>
         <run_address>0x2716</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_info</name>
         <load_address>0x2796</load_address>
         <run_address>0x2796</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_info</name>
         <load_address>0x285c</load_address>
         <run_address>0x285c</run_address>
         <size>0x8d2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0x312e</load_address>
         <run_address>0x312e</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_info</name>
         <load_address>0x31a3</load_address>
         <run_address>0x31a3</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x6315</load_address>
         <run_address>0x6315</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_info</name>
         <load_address>0x6738</load_address>
         <run_address>0x6738</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0x6e7c</load_address>
         <run_address>0x6e7c</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x6ec2</load_address>
         <run_address>0x6ec2</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_info</name>
         <load_address>0x7054</load_address>
         <run_address>0x7054</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0x714c</load_address>
         <run_address>0x714c</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_info</name>
         <load_address>0x7187</load_address>
         <run_address>0x7187</run_address>
         <size>0xa8</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_ranges</name>
         <load_address>0xb8</load_address>
         <run_address>0xb8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_ranges</name>
         <load_address>0x2d8</load_address>
         <run_address>0x2d8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x320</load_address>
         <run_address>0x320</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_ranges</name>
         <load_address>0x338</load_address>
         <run_address>0x338</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b5e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x1b5e</load_address>
         <run_address>0x1b5e</run_address>
         <size>0x15f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_str</name>
         <load_address>0x1cbd</load_address>
         <run_address>0x1cbd</run_address>
         <size>0x123</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_str</name>
         <load_address>0x1de0</load_address>
         <run_address>0x1de0</run_address>
         <size>0x4d7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x22b7</load_address>
         <run_address>0x22b7</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_str</name>
         <load_address>0x242f</load_address>
         <run_address>0x242f</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x4206</load_address>
         <run_address>0x4206</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x442b</load_address>
         <run_address>0x442b</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x475a</load_address>
         <run_address>0x475a</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x484f</load_address>
         <run_address>0x484f</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x49ea</load_address>
         <run_address>0x49ea</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x4b32</load_address>
         <run_address>0x4b32</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x128</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x158</load_address>
         <run_address>0x158</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x188</load_address>
         <run_address>0x188</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_frame</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_frame</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x640</load_address>
         <run_address>0x640</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x6d0</load_address>
         <run_address>0x6d0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_frame</name>
         <load_address>0x7d0</load_address>
         <run_address>0x7d0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_frame</name>
         <load_address>0x7f0</load_address>
         <run_address>0x7f0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_frame</name>
         <load_address>0x828</load_address>
         <run_address>0x828</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_frame</name>
         <load_address>0x858</load_address>
         <run_address>0x858</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x667</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x667</load_address>
         <run_address>0x667</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x71f</load_address>
         <run_address>0x71f</run_address>
         <size>0x86</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x7a5</load_address>
         <run_address>0x7a5</run_address>
         <size>0x2b7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0xa5c</load_address>
         <run_address>0xa5c</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_line</name>
         <load_address>0xbd5</load_address>
         <run_address>0xbd5</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x2344</load_address>
         <run_address>0x2344</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x2520</load_address>
         <run_address>0x2520</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x2a3a</load_address>
         <run_address>0x2a3a</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x2a78</load_address>
         <run_address>0x2a78</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x2b76</load_address>
         <run_address>0x2b76</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x2bdd</load_address>
         <run_address>0x2bdd</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x6a0</size>
         <contents>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x790</load_address>
         <run_address>0x790</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-134"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x760</load_address>
         <run_address>0x760</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-fd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1fc</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-137"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f4" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f5" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f6" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f7" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f8" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f9" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fb" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-117" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23b0</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-119" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xaf6</size>
         <contents>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-139"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11b" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x722f</size>
         <contents>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-138"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11d" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x350</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11f" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4c1b</size>
         <contents>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-e7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-121" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x878</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-c6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c1e</size>
         <contents>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-c5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-136" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-13d" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7a8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-13e" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1fc</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-13f" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x7a8</used_space>
         <unused_space>0x7858</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x6a0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x760</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x790</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x7a8</start_address>
               <size>0x7858</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x3fc</used_space>
         <unused_space>0x3c04</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f9"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-fb"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1fc</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001fc</start_address>
               <size>0x3c04</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x790</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1fc</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x79c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x7a4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x7a4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x798</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x79c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-61">
         <name>SYSCFG_DL_init</name>
         <value>0x4e9</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-62">
         <name>SYSCFG_DL_initPower</name>
         <value>0x405</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x459</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x4a9</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_PWM_L1_init</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_PWM_R1_init</name>
         <value>0x2e5</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_PWM_L2_init</name>
         <value>0x255</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-68">
         <name>SYSCFG_DL_PWM_R2_init</name>
         <value>0x375</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-69">
         <name>gPWM_R1Backup</name>
         <value>0x2020015c</value>
      </symbol>
      <symbol id="sm-6a">
         <name>gPWM_L2Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-6b">
         <name>gPWM_R2Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-76">
         <name>Default_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>Reset_Handler</name>
         <value>0x755</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-78">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-79">
         <name>NMI_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>HardFault_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>SVC_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>PendSV_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>SysTick_Handler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>GROUP0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>GROUP1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>TIMG8_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>UART3_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>ADC0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>ADC1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>CANFD0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>DAC0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>SPI0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>SPI1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>UART1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>UART2_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>UART0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMG0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>TIMG6_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMA0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>TIMA1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>TIMG7_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>TIMG12_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>I2C0_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>I2C1_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>AES_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>RTC_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>DMA_IRQHandler</name>
         <value>0x72f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>main</name>
         <value>0x565</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-b6">
         <name>init_motor</name>
         <value>0x66d</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-b7">
         <name>L1_control</name>
         <value>0x59d</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-b8">
         <name>L2_control</name>
         <value>0x5d1</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-b9">
         <name>R1_control</name>
         <value>0x605</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-ba">
         <name>R2_control</name>
         <value>0x639</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-bb">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-bc">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-bd">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-be">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-bf">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c0">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c1">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c2">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c3">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cc">
         <name>DL_Common_delayCycles</name>
         <value>0x741</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-e3">
         <name>DL_Timer_setClockConfig</name>
         <value>0x6e5</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-e4">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x731</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-e5">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x6c9</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-e6">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x701</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-e7">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-f2">
         <name>_c_int00_noargs</name>
         <value>0x6a1</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-f3">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-ff">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x529</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-107">
         <name>_system_pre_init</name>
         <value>0x759</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-112">
         <name>__TI_zero_init_nomemset</name>
         <value>0x719</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-11e">
         <name>abort</name>
         <value>0x74b</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-128">
         <name>HOSTexit</name>
         <value>0x751</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-129">
         <name>C$$EXIT</name>
         <value>0x750</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-12a">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12c">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12d">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
