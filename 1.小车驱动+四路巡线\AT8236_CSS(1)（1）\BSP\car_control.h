#ifndef __CAR_CONTROL_H_
#define __CAR_CONTROL_H_

#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"
#include "delay.h"

// 车辆运动状态定义
typedef enum {
    CAR_STOP = 0,      // 停止
    CAR_FORWARD,       // 前进
    CAR_BACKWARD,      // 后退
    CAR_TURN_LEFT,     // 左转
    CAR_TURN_RIGHT     // 右转
} car_state_t;

// 车辆控制参数
#define CAR_DEFAULT_SPEED    100    // 默认速度
#define CAR_TURN_SPEED       80     // 转弯速度
#define CAR_MAX_SPEED        1000   // 最大速度

// 车辆控制函数声明
void car_init(void);                                    // 车辆初始化
void car_stop(void);                                    // 停止
void car_forward(uint16_t speed);                       // 前进
void car_backward(uint16_t speed);                      // 后退
void car_turn_left(uint16_t speed);                     // 左转
void car_turn_right(uint16_t speed);                    // 右转
void car_set_speed(uint16_t left_speed, uint16_t right_speed, 
                   uint8_t left_dir, uint8_t right_dir); // 设置左右轮速度和方向

// 高级控制函数
void car_test_run(void);                                // 测试运行 - 转5秒后停止
car_state_t car_get_state(void);                        // 获取当前状态
void car_set_state(car_state_t state);                  // 设置状态

#endif
