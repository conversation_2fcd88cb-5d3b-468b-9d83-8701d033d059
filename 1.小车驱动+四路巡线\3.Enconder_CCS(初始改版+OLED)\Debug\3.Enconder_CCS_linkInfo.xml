<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x66a0a87d</link_time>
   <link_errors>0x0</link_errors>
   <output_file>W:\CCS\3.Enconder_CCS\Debug\3.Enconder_CCS.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xd75</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_at8236.o</file>
         <name>bsp_at8236.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_delay.o</file>
         <name>bsp_delay.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_enconder.o</file>
         <name>bsp_enconder.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>W:\CCS\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_usart.o</file>
         <name>bsp_usart.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>W:\CCS\3.Enconder_CCS\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>W:\MSPM0G3507\mspm0_sdk_1_30_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>W:\MSPM0G3507\mspm0_sdk_1_30_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>W:\MSPM0G3507\mspm0_sdk_1_30_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-26">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-aa">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ab">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-ac">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-ad">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-ae">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-af">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-b0">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-b1">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-b2">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-b3">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-b4">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-b5">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-b6">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-b7">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-b8">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-b9">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-ba">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-bb">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-bc">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-bd">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-be">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-bf">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-c0">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-c1">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-c2">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-c3">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-c4">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-c5">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-c6">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-c7">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-c8">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-c9">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-ca">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-cb">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-cc">
         <path>D:\CCS\ti\ccs1271\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text:__TI_printfi_minimal</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x284</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.DL_Timer_initPWMMode</name>
         <load_address>0x344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x344</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x408</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x408</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text:memcpy</name>
         <load_address>0x4a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4a4</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x53e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x53e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x540</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x540</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_PWM_L1_init</name>
         <load_address>0x5d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d8</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_PWM_L2_init</name>
         <load_address>0x658</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x658</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.SYSCFG_DL_PWM_R1_init</name>
         <load_address>0x6d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d8</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_PWM_R2_init</name>
         <load_address>0x758</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x758</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_TimerA_initPWMMode</name>
         <load_address>0x7d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d8</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x850</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x850</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_MYUART_init</name>
         <load_address>0x8c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8c8</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.main</name>
         <load_address>0x938</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x938</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text:memset</name>
         <load_address>0x9a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9a4</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xa06</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa06</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xa08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa08</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0xa68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa68</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_UART_init</name>
         <load_address>0xabc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xabc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xb04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb04</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xb4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb4c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xb8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb8c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xbc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbc8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0xc04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc04</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.sprintf</name>
         <load_address>0xc3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc3c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.L1_control</name>
         <load_address>0xc74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc74</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.L2_control</name>
         <load_address>0xca8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca8</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.init_motor</name>
         <load_address>0xcdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcdc</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.uart0_send_string</name>
         <load_address>0xd10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd10</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0xd44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd44</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xd74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd74</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.memccpy</name>
         <load_address>0xd9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd9c</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xdc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdc0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xddc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xddc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.delay_ms</name>
         <load_address>0xdf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0xe14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe14</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text._outs</name>
         <load_address>0xe2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe2c</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xe44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe44</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0xe5a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe5a</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xe6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe6c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0xe80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe80</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.__aeabi_memset</name>
         <load_address>0xe90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe90</run_address>
         <size>0xe</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.text.strlen</name>
         <load_address>0xe9e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe9e</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xeac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeac</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text._outc</name>
         <load_address>0xeb6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeb6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c2"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xec8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec8</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.text._system_pre_init</name>
         <load_address>0xecc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xecc</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text:abort</name>
         <load_address>0xed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed0</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-206">
         <name>__TI_handler_table</name>
         <load_address>0xf48</load_address>
         <readonly>true</readonly>
         <run_address>0xf48</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-208">
         <name>.cinit..data.load</name>
         <load_address>0xf54</load_address>
         <readonly>true</readonly>
         <run_address>0xf54</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-209">
         <name>.cinit..bss.load</name>
         <load_address>0xf60</load_address>
         <readonly>true</readonly>
         <run_address>0xf60</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-207">
         <name>__TI_cinit_table</name>
         <load_address>0xf68</load_address>
         <readonly>true</readonly>
         <run_address>0xf68</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b1">
         <name>.rodata.str1.176328792963337784071</name>
         <load_address>0xed8</load_address>
         <readonly>true</readonly>
         <run_address>0xed8</run_address>
         <size>0x23</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-133">
         <name>.rodata.str1.44690500295887128011</name>
         <load_address>0xefb</load_address>
         <readonly>true</readonly>
         <run_address>0xefb</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-114">
         <name>.rodata.gMYUARTConfig</name>
         <load_address>0xf0c</load_address>
         <readonly>true</readonly>
         <run_address>0xf0c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-113">
         <name>.rodata.gMYUARTClockConfig</name>
         <load_address>0xf16</load_address>
         <readonly>true</readonly>
         <run_address>0xf16</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.rodata.gPWM_L1Config</name>
         <load_address>0xf18</load_address>
         <readonly>true</readonly>
         <run_address>0xf18</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-108">
         <name>.rodata.gPWM_L2Config</name>
         <load_address>0xf20</load_address>
         <readonly>true</readonly>
         <run_address>0xf20</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.rodata.gPWM_R1Config</name>
         <load_address>0xf28</load_address>
         <readonly>true</readonly>
         <run_address>0xf28</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.rodata.gPWM_R2Config</name>
         <load_address>0xf30</load_address>
         <readonly>true</readonly>
         <run_address>0xf30</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-103">
         <name>.rodata.gPWM_L1ClockConfig</name>
         <load_address>0xf38</load_address>
         <readonly>true</readonly>
         <run_address>0xf38</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.rodata.gPWM_L2ClockConfig</name>
         <load_address>0xf3b</load_address>
         <readonly>true</readonly>
         <run_address>0xf3b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-105">
         <name>.rodata.gPWM_R1ClockConfig</name>
         <load_address>0xf3e</load_address>
         <readonly>true</readonly>
         <run_address>0xf3e</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.rodata.gPWM_R2ClockConfig</name>
         <load_address>0xf41</load_address>
         <readonly>true</readonly>
         <run_address>0xf41</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-67">
         <name>.data.speed</name>
         <load_address>0x202001e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.data.speed2</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.data.buf</name>
         <load_address>0x202001b0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001b0</run_address>
         <size>0x23</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-64">
         <name>.data.delay_times</name>
         <load_address>0x202001d4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-65">
         <name>.data.getspeed</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-66">
         <name>.data.gEncoderCount_L1</name>
         <load_address>0x202001d8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.data.gEncoderCount_L2</name>
         <load_address>0x202001dc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001dc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-71">
         <name>.data.uart_data</name>
         <load_address>0x202001d3</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d3</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.common:gPWM_R1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200134</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-dd">
         <name>.common:gPWM_L2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.common:gPWM_R2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6e">
         <name>.common:gpioA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001ac</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-20b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_loc</name>
         <load_address>0xee</load_address>
         <run_address>0xee</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_loc</name>
         <load_address>0x286</load_address>
         <run_address>0x286</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_loc</name>
         <load_address>0x2ae</load_address>
         <run_address>0x2ae</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_loc</name>
         <load_address>0x2d4</load_address>
         <run_address>0x2d4</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_loc</name>
         <load_address>0x2e7</load_address>
         <run_address>0x2e7</run_address>
         <size>0x14e3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_loc</name>
         <load_address>0x17ca</load_address>
         <run_address>0x17ca</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_loc</name>
         <load_address>0x1f86</load_address>
         <run_address>0x1f86</run_address>
         <size>0x136</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_loc</name>
         <load_address>0x20bc</load_address>
         <run_address>0x20bc</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_loc</name>
         <load_address>0x2194</load_address>
         <run_address>0x2194</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_loc</name>
         <load_address>0x2614</load_address>
         <run_address>0x2614</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_loc</name>
         <load_address>0x2780</load_address>
         <run_address>0x2780</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x27ef</load_address>
         <run_address>0x27ef</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_loc</name>
         <load_address>0x2955</load_address>
         <run_address>0x2955</run_address>
         <size>0x33d1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x5d26</load_address>
         <run_address>0x5d26</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_loc</name>
         <load_address>0x5d4c</load_address>
         <run_address>0x5d4c</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.debug_loc</name>
         <load_address>0x5e0b</load_address>
         <run_address>0x5e0b</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x290</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_abbrev</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_abbrev</name>
         <load_address>0x2fd</load_address>
         <run_address>0x2fd</run_address>
         <size>0x121</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x41e</load_address>
         <run_address>0x41e</run_address>
         <size>0x10f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_abbrev</name>
         <load_address>0x52d</load_address>
         <run_address>0x52d</run_address>
         <size>0x76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_abbrev</name>
         <load_address>0x5a3</load_address>
         <run_address>0x5a3</run_address>
         <size>0x10b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_abbrev</name>
         <load_address>0x6ae</load_address>
         <run_address>0x6ae</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_abbrev</name>
         <load_address>0x83b</load_address>
         <run_address>0x83b</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_abbrev</name>
         <load_address>0x89d</load_address>
         <run_address>0x89d</run_address>
         <size>0x267</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_abbrev</name>
         <load_address>0xb04</load_address>
         <run_address>0xb04</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0xd83</load_address>
         <run_address>0xd83</run_address>
         <size>0xe1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_abbrev</name>
         <load_address>0xe64</load_address>
         <run_address>0xe64</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_abbrev</name>
         <load_address>0xf13</load_address>
         <run_address>0xf13</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_abbrev</name>
         <load_address>0x1099</load_address>
         <run_address>0x1099</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_abbrev</name>
         <load_address>0x10d2</load_address>
         <run_address>0x10d2</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_abbrev</name>
         <load_address>0x1194</load_address>
         <run_address>0x1194</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x1204</load_address>
         <run_address>0x1204</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0x1291</load_address>
         <run_address>0x1291</run_address>
         <size>0x2f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_abbrev</name>
         <load_address>0x1586</load_address>
         <run_address>0x1586</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_abbrev</name>
         <load_address>0x1639</load_address>
         <run_address>0x1639</run_address>
         <size>0x8b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_abbrev</name>
         <load_address>0x16c4</load_address>
         <run_address>0x16c4</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c2"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_abbrev</name>
         <load_address>0x16eb</load_address>
         <run_address>0x16eb</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_abbrev</name>
         <load_address>0x1710</load_address>
         <run_address>0x1710</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c4"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.debug_abbrev</name>
         <load_address>0x1737</load_address>
         <run_address>0x1737</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x1790</load_address>
         <run_address>0x1790</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_abbrev</name>
         <load_address>0x17b5</load_address>
         <run_address>0x17b5</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-20d">
         <name>.debug_abbrev</name>
         <load_address>0x17da</load_address>
         <run_address>0x17da</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3f08</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3f08</load_address>
         <run_address>0x3f08</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_info</name>
         <load_address>0x3f88</load_address>
         <run_address>0x3f88</run_address>
         <size>0x36b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0x42f3</load_address>
         <run_address>0x42f3</run_address>
         <size>0x8a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x4b99</load_address>
         <run_address>0x4b99</run_address>
         <size>0x8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_info</name>
         <load_address>0x4c28</load_address>
         <run_address>0x4c28</run_address>
         <size>0x838</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_info</name>
         <load_address>0x5460</load_address>
         <run_address>0x5460</run_address>
         <size>0x722</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_info</name>
         <load_address>0x5b82</load_address>
         <run_address>0x5b82</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_info</name>
         <load_address>0x5bf7</load_address>
         <run_address>0x5bf7</run_address>
         <size>0x2b1c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0x8713</load_address>
         <run_address>0x8713</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0x996c</load_address>
         <run_address>0x996c</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x9ad1</load_address>
         <run_address>0x9ad1</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_info</name>
         <load_address>0x9ef4</load_address>
         <run_address>0x9ef4</run_address>
         <size>0x74a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0xa63e</load_address>
         <run_address>0xa63e</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_info</name>
         <load_address>0xa684</load_address>
         <run_address>0xa684</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xa816</load_address>
         <run_address>0xa816</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xa8dc</load_address>
         <run_address>0xa8dc</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_info</name>
         <load_address>0xaa5c</load_address>
         <run_address>0xaa5c</run_address>
         <size>0x1f4b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_info</name>
         <load_address>0xc9a7</load_address>
         <run_address>0xc9a7</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_info</name>
         <load_address>0xca94</load_address>
         <run_address>0xca94</run_address>
         <size>0xce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0xcb62</load_address>
         <run_address>0xcb62</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c2"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0xcd00</load_address>
         <run_address>0xcd00</run_address>
         <size>0x1ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_info</name>
         <load_address>0xceba</load_address>
         <run_address>0xceba</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c4"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_info</name>
         <load_address>0xd07b</load_address>
         <run_address>0xd07b</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0xd100</load_address>
         <run_address>0xd100</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_info</name>
         <load_address>0xd3fa</load_address>
         <run_address>0xd3fa</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-20c">
         <name>.debug_info</name>
         <load_address>0xd63e</load_address>
         <run_address>0xd63e</run_address>
         <size>0x8f</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_ranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_ranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_ranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_ranges</name>
         <load_address>0x2a8</load_address>
         <run_address>0x2a8</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0x438</load_address>
         <run_address>0x438</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_ranges</name>
         <load_address>0x458</load_address>
         <run_address>0x458</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_ranges</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_ranges</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_ranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_ranges</name>
         <load_address>0x590</load_address>
         <run_address>0x590</run_address>
         <size>0x1a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_ranges</name>
         <load_address>0x730</load_address>
         <run_address>0x730</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_ranges</name>
         <load_address>0x748</load_address>
         <run_address>0x748</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_ranges</name>
         <load_address>0x770</load_address>
         <run_address>0x770</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_ranges</name>
         <load_address>0x788</load_address>
         <run_address>0x788</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x27bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_str</name>
         <load_address>0x27bc</load_address>
         <run_address>0x27bc</run_address>
         <size>0x14e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_str</name>
         <load_address>0x290a</load_address>
         <run_address>0x290a</run_address>
         <size>0x41e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x2d28</load_address>
         <run_address>0x2d28</run_address>
         <size>0x4be</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_str</name>
         <load_address>0x31e6</load_address>
         <run_address>0x31e6</run_address>
         <size>0xe5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_str</name>
         <load_address>0x32cb</load_address>
         <run_address>0x32cb</run_address>
         <size>0x4c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_str</name>
         <load_address>0x378c</load_address>
         <run_address>0x378c</run_address>
         <size>0x55f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_str</name>
         <load_address>0x3ceb</load_address>
         <run_address>0x3ceb</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_str</name>
         <load_address>0x3e62</load_address>
         <run_address>0x3e62</run_address>
         <size>0x17f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_str</name>
         <load_address>0x565b</load_address>
         <run_address>0x565b</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x6348</load_address>
         <run_address>0x6348</run_address>
         <size>0x164</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_str</name>
         <load_address>0x64ac</load_address>
         <run_address>0x64ac</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_str</name>
         <load_address>0x66d1</load_address>
         <run_address>0x66d1</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_str</name>
         <load_address>0x6a00</load_address>
         <run_address>0x6a00</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_str</name>
         <load_address>0x6af5</load_address>
         <run_address>0x6af5</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_str</name>
         <load_address>0x6c90</load_address>
         <run_address>0x6c90</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x6df8</load_address>
         <run_address>0x6df8</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_str</name>
         <load_address>0x6fcd</load_address>
         <run_address>0x6fcd</run_address>
         <size>0x901</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_str</name>
         <load_address>0x78ce</load_address>
         <run_address>0x78ce</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_str</name>
         <load_address>0x7a0d</load_address>
         <run_address>0x7a0d</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.debug_str</name>
         <load_address>0x7b34</load_address>
         <run_address>0x7b34</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x154</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x154</load_address>
         <run_address>0x154</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x184</load_address>
         <run_address>0x184</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0x1a4</load_address>
         <run_address>0x1a4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x234</load_address>
         <run_address>0x234</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0x264</load_address>
         <run_address>0x264</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_frame</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_frame</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_frame</name>
         <load_address>0x2f0</load_address>
         <run_address>0x2f0</run_address>
         <size>0x360</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_frame</name>
         <load_address>0x650</load_address>
         <run_address>0x650</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_frame</name>
         <load_address>0x808</load_address>
         <run_address>0x808</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_frame</name>
         <load_address>0x860</load_address>
         <run_address>0x860</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0x8f0</load_address>
         <run_address>0x8f0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_frame</name>
         <load_address>0x9f0</load_address>
         <run_address>0x9f0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_frame</name>
         <load_address>0xa10</load_address>
         <run_address>0xa10</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0xa48</load_address>
         <run_address>0xa48</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xa70</load_address>
         <run_address>0xa70</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_frame</name>
         <load_address>0xaa0</load_address>
         <run_address>0xaa0</run_address>
         <size>0x480</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0xf20</load_address>
         <run_address>0xf20</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_frame</name>
         <load_address>0xf50</load_address>
         <run_address>0xf50</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_frame</name>
         <load_address>0xf7c</load_address>
         <run_address>0xf7c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x77c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x77c</load_address>
         <run_address>0x77c</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x83e</load_address>
         <run_address>0x83e</run_address>
         <size>0x1ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0xa2d</load_address>
         <run_address>0xa2d</run_address>
         <size>0x2d3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0xd00</load_address>
         <run_address>0xd00</run_address>
         <size>0xad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0xdad</load_address>
         <run_address>0xdad</run_address>
         <size>0x2af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_line</name>
         <load_address>0x105c</load_address>
         <run_address>0x105c</run_address>
         <size>0x316</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_line</name>
         <load_address>0x1372</load_address>
         <run_address>0x1372</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_line</name>
         <load_address>0x1456</load_address>
         <run_address>0x1456</run_address>
         <size>0x1393</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_line</name>
         <load_address>0x27e9</load_address>
         <run_address>0x27e9</run_address>
         <size>0x989</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x3172</load_address>
         <run_address>0x3172</run_address>
         <size>0x108</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x327a</load_address>
         <run_address>0x327a</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_line</name>
         <load_address>0x3478</load_address>
         <run_address>0x3478</run_address>
         <size>0x4fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_line</name>
         <load_address>0x3973</load_address>
         <run_address>0x3973</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_line</name>
         <load_address>0x39b1</load_address>
         <run_address>0x39b1</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x3aa9</load_address>
         <run_address>0x3aa9</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x3b68</load_address>
         <run_address>0x3b68</run_address>
         <size>0x1c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_line</name>
         <load_address>0x3d2f</load_address>
         <run_address>0x3d2f</run_address>
         <size>0x1c54</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x5983</load_address>
         <run_address>0x5983</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_line</name>
         <load_address>0x59ee</load_address>
         <run_address>0x59ee</run_address>
         <size>0xd1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x5abf</load_address>
         <run_address>0x5abf</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c2"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_line</name>
         <load_address>0x5b63</load_address>
         <run_address>0x5b63</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0x5c1d</load_address>
         <run_address>0x5c1d</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c4"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_line</name>
         <load_address>0x5cdf</load_address>
         <run_address>0x5cdf</run_address>
         <size>0xb7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c9"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_line</name>
         <load_address>0x5d96</load_address>
         <run_address>0x5d96</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_line</name>
         <load_address>0x5e36</load_address>
         <run_address>0x5e36</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c2"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c3"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c4"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_aranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cb"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_aranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cc"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xe18</size>
         <contents>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-b2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xf48</load_address>
         <run_address>0xf48</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-206"/>
            <object_component_ref idref="oc-208"/>
            <object_component_ref idref="oc-209"/>
            <object_component_ref idref="oc-207"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xed8</load_address>
         <run_address>0xed8</run_address>
         <size>0x70</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-10a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001b0</run_address>
         <size>0x3c</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-71"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1b0</size>
         <contents>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-20b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c5" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c6" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c7" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c8" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c9" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ca" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1cc" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e8" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5e2b</size>
         <contents>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-1a7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ea" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17e9</size>
         <contents>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-20d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ec" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd6cd</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-20c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ee" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d8</size>
         <contents>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f0" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7ccc</size>
         <contents>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-1a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f2" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfac</size>
         <contents>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-15e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f4" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5eb6</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-200" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb8</size>
         <contents>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20a" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-213" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf78</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-214" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1ec</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-215" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xf78</used_space>
         <unused_space>0x1f088</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xe18</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xed8</start_address>
               <size>0x70</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xf48</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xf78</start_address>
               <size>0x1f088</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x3ec</used_space>
         <unused_space>0x7c14</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1ca"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1cc"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1b0</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202001b0</start_address>
               <size>0x3c</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001ec</start_address>
               <size>0x7c14</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0xf54</load_address>
            <load_size>0xb</load_size>
            <run_address>0x202001b0</run_address>
            <run_size>0x3c</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0xf60</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1b0</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xf68</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xf78</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xf78</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xf48</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xf54</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6c">
         <name>SYSCFG_DL_init</name>
         <value>0xb05</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-6d">
         <name>SYSCFG_DL_initPower</name>
         <value>0xa09</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x541</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xb8d</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-70">
         <name>SYSCFG_DL_PWM_L1_init</name>
         <value>0x5d9</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-71">
         <name>SYSCFG_DL_PWM_R1_init</name>
         <value>0x6d9</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-72">
         <name>SYSCFG_DL_PWM_L2_init</name>
         <value>0x659</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-73">
         <name>SYSCFG_DL_PWM_R2_init</name>
         <value>0x759</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-74">
         <name>SYSCFG_DL_MYUART_init</name>
         <value>0x8c9</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-75">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0xd45</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-76">
         <name>gPWM_R1Backup</name>
         <value>0x20200134</value>
      </symbol>
      <symbol id="sm-77">
         <name>gPWM_L2Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-78">
         <name>gPWM_R2Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-83">
         <name>Default_Handler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>Reset_Handler</name>
         <value>0xec9</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-85">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-86">
         <name>NMI_Handler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>HardFault_Handler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>SVC_Handler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>PendSV_Handler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>GROUP0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMG8_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>UART3_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>ADC0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>ADC1_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>CANFD0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>DAC0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>SPI0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>SPI1_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>UART1_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>UART2_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>TIMG0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG6_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>TIMA0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>TIMA1_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>TIMG7_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>TIMG12_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>I2C0_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>I2C1_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>AES_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>RTC_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>DMA_IRQHandler</name>
         <value>0x53f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a9">
         <name>main</name>
         <value>0x939</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-aa">
         <name>speed</name>
         <value>0x202001e8</value>
         <object_component_ref idref="oc-67"/>
      </symbol>
      <symbol id="sm-ab">
         <name>speed2</name>
         <value>0x202001e4</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-ac">
         <name>buf</name>
         <value>0x202001b0</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-bd">
         <name>init_motor</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-be">
         <name>L1_control</name>
         <value>0xc75</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-bf">
         <name>L2_control</name>
         <value>0xca9</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-cd">
         <name>delay_ms</name>
         <value>0xdf9</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-ce">
         <name>delay_times</name>
         <value>0x202001d4</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-cf">
         <name>SysTick_Handler</name>
         <value>0xa69</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-d0">
         <name>getspeed</name>
         <value>0x202001e0</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-d9">
         <name>GROUP1_IRQHandler</name>
         <value>0x409</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-da">
         <name>gpioA</name>
         <value>0x202001ac</value>
      </symbol>
      <symbol id="sm-db">
         <name>gEncoderCount_L1</name>
         <value>0x202001d8</value>
         <object_component_ref idref="oc-66"/>
      </symbol>
      <symbol id="sm-dc">
         <name>gEncoderCount_L2</name>
         <value>0x202001dc</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-ea">
         <name>uart0_send_string</name>
         <value>0xd11</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-eb">
         <name>UART0_IRQHandler</name>
         <value>0xc05</value>
         <object_component_ref idref="oc-3d"/>
      </symbol>
      <symbol id="sm-ec">
         <name>uart_data</name>
         <value>0x202001d3</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-ed">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f0">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f1">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f2">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f3">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f4">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f5">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fe">
         <name>DL_Common_delayCycles</name>
         <value>0xead</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-118">
         <name>DL_Timer_setClockConfig</name>
         <value>0xddd</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-119">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0xe81</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-11a">
         <name>DL_Timer_initPWMMode</name>
         <value>0x345</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-11b">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0xe15</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-11c">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xdc1</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-11d">
         <name>DL_TimerA_initPWMMode</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-109"/>
      </symbol>
      <symbol id="sm-12a">
         <name>DL_UART_init</name>
         <value>0xabd</value>
         <object_component_ref idref="oc-112"/>
      </symbol>
      <symbol id="sm-12b">
         <name>DL_UART_setClockConfig</name>
         <value>0xe5b</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-13c">
         <name>sprintf</name>
         <value>0xc3d</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-147">
         <name>_c_int00_noargs</name>
         <value>0xd75</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-148">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-154">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xbc9</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-15c">
         <name>_system_pre_init</name>
         <value>0xecd</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-167">
         <name>__TI_zero_init_nomemset</name>
         <value>0xe45</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-170">
         <name>__TI_decompress_none</name>
         <value>0xe6d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-17b">
         <name>__TI_decompress_lzss</name>
         <value>0x851</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-191">
         <name>__TI_printfi_minimal</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-115"/>
      </symbol>
      <symbol id="sm-19e">
         <name>abort</name>
         <value>0xed1</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-19f">
         <name>C$$EXIT</name>
         <value>0xed0</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>memccpy</name>
         <value>0xd9d</value>
         <object_component_ref idref="oc-12f"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>__aeabi_memcpy</name>
         <value>0xec1</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>__aeabi_memcpy4</name>
         <value>0xec1</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>__aeabi_memcpy8</name>
         <value>0xec1</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__aeabi_memset</name>
         <value>0xe91</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1d6">
         <name>__aeabi_memset4</name>
         <value>0xe91</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1d7">
         <name>__aeabi_memset8</name>
         <value>0xe91</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1dd">
         <name>__aeabi_uidiv</name>
         <value>0xb4d</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-1de">
         <name>__aeabi_uidivmod</name>
         <value>0xb4d</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-1e9">
         <name>__aeabi_idiv0</name>
         <value>0xa07</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-203">
         <name>memcpy</name>
         <value>0x4a5</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-212">
         <name>memset</name>
         <value>0x9a5</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-213">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-216">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-217">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
