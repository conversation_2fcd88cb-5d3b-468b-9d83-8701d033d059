#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"
#include "car_control.h"
#include "Four_linewalking.h"
#include "delay.h"
#include "usart.h"
#include <stdio.h>

// 系统状态定义
typedef enum {
    SYSTEM_INIT = 0,      // 系统初始化
    SYSTEM_TEST,          // 测试运行阶段
    SYSTEM_LINE_FOLLOW    // 循迹运行阶段
} system_state_t;

static system_state_t system_state = SYSTEM_INIT;

//主函数
int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // 初始化各个模块
    USART_Init();           // 串口初始化
    line_follow_init();     // 循迹模块初始化（包含车辆控制初始化）

    // 短暂延时确保初始化完成
    delay_ms(100);

    printf("System initialized. Starting test run...\r\n");

    // 测试运行：前进5秒后停止
    car_forward(CAR_DEFAULT_SPEED);
    delay_ms(5000);  // 延时5秒
    car_stop();

    printf("Test run completed. Entering line following mode...\r\n");

    // 进入循迹模式
    system_state = SYSTEM_LINE_FOLLOW;

    while (1)
    {
        switch(system_state)
        {
            case SYSTEM_LINE_FOLLOW:
                // 执行简化的直线循迹
                simple_line_follow();
                break;

            default:
                car_stop();
                break;
        }
    }
}