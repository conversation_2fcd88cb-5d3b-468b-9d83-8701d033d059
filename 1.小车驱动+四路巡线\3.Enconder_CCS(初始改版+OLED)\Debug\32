********************************************************************************
TI ARM Clang Hex Converter                                                v3.2.2
********************************************************************************

INPUT FILE NAME: <3.Enconder_CCS.out>
OUTPUT FORMAT:   Extended Tektronix

PHYSICAL MEMORY PARAMETERS
   Default data width   :   8
   Default memory width :  32
   Default output width :  32


OUTPUT TRANSLATION MAP
--------------------------------------------------------------------------------
00000000..ffffffff  Page=0  Memory Width=32  ROM Width=32
--------------------------------------------------------------------------------
   OUTPUT FILES: 3.Enconder_CCS.hex [b0..b31]

   CONTENTS: 00000000..000000bf   3.Enconder_CCS.out(.intvecs)
             000000c0..00000ed7   3.Enconder_CCS.out(.text)
             00000ed8..00000f47   3.Enconder_CCS.out(.rodata)
             00000f48..00000f77   3.Enconder_CCS.out(.cinit)
