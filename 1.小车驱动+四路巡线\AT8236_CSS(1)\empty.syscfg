/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 * 双驱动小车配置 - 只使用两路PWM控制
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();  // 左侧电机PWM
const PWM2    = PWM.addInstance();  // 右侧电机PWM
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 * 双驱动小车配置
 */
// 循迹传感器GPIO配置 - 四路传感器
GPIO1.$name                          = "sensor";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "X1";
GPIO1.associatedPins[0].direction    = "INPUT";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].pin.$assign  = "PA24";
GPIO1.associatedPins[1].$name        = "X2";
GPIO1.associatedPins[1].direction    = "INPUT";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].pin.$assign  = "PA25";
GPIO1.associatedPins[2].$name        = "X3";
GPIO1.associatedPins[2].direction    = "INPUT";
GPIO1.associatedPins[2].assignedPort = "PORTA";
GPIO1.associatedPins[2].pin.$assign  = "PA26";
GPIO1.associatedPins[3].$name        = "X4";
GPIO1.associatedPins[3].direction    = "INPUT";
GPIO1.associatedPins[3].assignedPort = "PORTA";
GPIO1.associatedPins[3].pin.$assign  = "PA27";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

// 左侧电机PWM配置 (PWM_L1)
PWM1.clockDivider                       = 8;
PWM1.$name                              = "PWM_L1";
PWM1.clockPrescale                      = 40;
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";

// 右侧电机PWM配置 (PWM_R1)
PWM2.clockDivider                       = 8;
PWM2.$name                              = "PWM_R1";
PWM2.clockPrescale                      = 40;
PWM2.peripheral.$assign                 = "TIMG6";
PWM2.peripheral.ccp0Pin.$assign         = "PA21";
PWM2.peripheral.ccp1Pin.$assign         = "PA22";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

// 系统时钟配置
SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

// SysTick定时器配置 - 用于精确延时
SYSTICK.periodEnable  = true;
SYSTICK.period        = 32;
SYSTICK.systickEnable = true;

// 串口配置 - 用于调试输出
UART1.$name                    = "UART_0";
UART1.peripheral.rxPin.$assign = "PA9";
UART1.peripheral.txPin.$assign = "PA8";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric7";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 * 双驱动小车引脚分配方案
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
PWM1.peripheral.$suggestSolution           = "TIMG0";
UART1.peripheral.$suggestSolution          = "UART1";
